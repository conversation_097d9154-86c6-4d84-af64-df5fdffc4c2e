import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="page-container">
      <div class="page-header">
        <h1>Profile</h1>
      </div>
      <div class="page-content">
        <p class="text-center text-gray-500 py-8">Profile placeholder</p>
      </div>
    </div>
  `,
  styles: [`
    h1 {
      font-size: var(--font-size-3xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-gray-900);
      margin: 0;
    }
  `]
})
export class ProfileComponent {
}
