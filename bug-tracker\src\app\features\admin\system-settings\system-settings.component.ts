import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';

interface SystemSetting {
  key: string;
  value: any;
  type: 'string' | 'number' | 'boolean' | 'select' | 'json';
  category: string;
  displayName: string;
  description: string;
  options?: string[];
  required?: boolean;
  validation?: any;
}

interface SettingCategory {
  name: string;
  displayName: string;
  description: string;
  icon: string;
  settings: SystemSetting[];
}

@Component({
  selector: 'app-system-settings',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, FormsModule],
  template: `
    <div class="system-settings">
      <!-- Header -->
      <div class="system-settings__header">
        <div class="system-settings__title-section">
          <h1 class="system-settings__title">System Settings</h1>
          <p class="system-settings__subtitle">Configure application settings and preferences</p>
        </div>
        <div class="system-settings__actions">
          <button
            type="button"
            class="btn btn-secondary"
            (click)="resetToDefaults()"
            [disabled]="saving"
          >
            Reset to Defaults
          </button>
          <button
            type="button"
            class="btn btn-primary"
            (click)="saveSettings()"
            [disabled]="!hasChanges || saving"
          >
            <span *ngIf="saving" class="btn-spinner"></span>
            {{ saving ? 'Saving...' : 'Save Changes' }}
          </button>
        </div>
      </div>

      <!-- Success/Error Messages -->
      <div *ngIf="successMessage" class="alert alert-success">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M9 12l2 2 4-4"></path>
          <circle cx="12" cy="12" r="10"></circle>
        </svg>
        {{ successMessage }}
      </div>

      <div *ngIf="errorMessage" class="alert alert-error">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="15" y1="9" x2="9" y2="15"></line>
          <line x1="9" y1="9" x2="15" y2="15"></line>
        </svg>
        {{ errorMessage }}
      </div>

      <!-- Loading State -->
      <div *ngIf="loading" class="loading-state">
        <div class="loading-spinner"></div>
        <p>Loading settings...</p>
      </div>

      <!-- Settings Categories -->
      <div *ngIf="!loading" class="settings-container">
        <!-- Category Navigation -->
        <div class="settings-nav">
          <div class="settings-nav__title">Categories</div>
          <nav class="settings-nav__list">
            <button
              *ngFor="let category of settingCategories; trackBy: trackByCategory"
              class="settings-nav__item"
              [class.settings-nav__item--active]="selectedCategory === category.name"
              (click)="selectCategory(category.name)"
            >
              <div class="settings-nav__icon" [innerHTML]="category.icon"></div>
              <div class="settings-nav__info">
                <div class="settings-nav__name">{{ category.displayName }}</div>
                <div class="settings-nav__description">{{ category.description }}</div>
              </div>
            </button>
          </nav>
        </div>

        <!-- Settings Content -->
        <div class="settings-content">
          <div *ngIf="selectedCategoryData" class="settings-section">
            <div class="settings-section__header">
              <h2 class="settings-section__title">{{ selectedCategoryData.displayName }}</h2>
              <p class="settings-section__description">{{ selectedCategoryData.description }}</p>
            </div>

            <form [formGroup]="settingsForm" class="settings-form">
              <div *ngFor="let setting of selectedCategoryData.settings; trackBy: trackBySetting"
                   class="setting-item">
                <div class="setting-item__header">
                  <label [for]="setting.key" class="setting-item__label">
                    {{ setting.displayName }}
                    <span *ngIf="setting.required" class="required-indicator">*</span>
                  </label>
                  <div class="setting-item__description">{{ setting.description }}</div>
                </div>

                <div class="setting-item__control">
                  <!-- String Input -->
                  <input
                    *ngIf="setting.type === 'string'"
                    [id]="setting.key"
                    type="text"
                    class="form-input"
                    [formControlName]="setting.key"
                    [placeholder]="'Enter ' + setting.displayName.toLowerCase()"
                  />

                  <!-- Number Input -->
                  <input
                    *ngIf="setting.type === 'number'"
                    [id]="setting.key"
                    type="number"
                    class="form-input"
                    [formControlName]="setting.key"
                    [placeholder]="'Enter ' + setting.displayName.toLowerCase()"
                  />

                  <!-- Boolean Toggle -->
                  <div *ngIf="setting.type === 'boolean'" class="toggle-switch">
                    <input
                      [id]="setting.key"
                      type="checkbox"
                      class="toggle-input"
                      [formControlName]="setting.key"
                    />
                    <label [for]="setting.key" class="toggle-label">
                      <span class="toggle-slider"></span>
                    </label>
                    <span class="toggle-text">
                      {{ settingsForm.get(setting.key)?.value ? 'Enabled' : 'Disabled' }}
                    </span>
                  </div>

                  <!-- Select Dropdown -->
                  <select
                    *ngIf="setting.type === 'select'"
                    [id]="setting.key"
                    class="form-select"
                    [formControlName]="setting.key"
                  >
                    <option value="">Select {{ setting.displayName.toLowerCase() }}</option>
                    <option *ngFor="let option of setting.options" [value]="option">
                      {{ option }}
                    </option>
                  </select>

                  <!-- JSON Textarea -->
                  <textarea
                    *ngIf="setting.type === 'json'"
                    [id]="setting.key"
                    class="form-textarea"
                    rows="6"
                    [formControlName]="setting.key"
                    [placeholder]="'Enter valid JSON for ' + setting.displayName.toLowerCase()"
                  ></textarea>

                  <!-- Validation Errors -->
                  <div *ngIf="settingsForm.get(setting.key)?.invalid && settingsForm.get(setting.key)?.touched"
                       class="form-error">
                    <span *ngIf="settingsForm.get(setting.key)?.errors?.['required']">
                      {{ setting.displayName }} is required
                    </span>
                    <span *ngIf="settingsForm.get(setting.key)?.errors?.['min']">
                      Value must be at least {{ setting.validation?.min }}
                    </span>
                    <span *ngIf="settingsForm.get(setting.key)?.errors?.['max']">
                      Value must be at most {{ setting.validation?.max }}
                    </span>
                    <span *ngIf="settingsForm.get(setting.key)?.errors?.['invalidJson']">
                      Please enter valid JSON
                    </span>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .system-settings {
      padding: var(--spacing-6);
      max-width: 1400px;
      margin: 0 auto;
    }

    .system-settings__header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: var(--spacing-6);
      gap: var(--spacing-4);
    }

    .system-settings__title {
      font-size: var(--font-size-3xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-gray-900);
      margin: 0 0 var(--spacing-1) 0;
    }

    .system-settings__subtitle {
      color: var(--color-gray-600);
      margin: 0;
    }

    .system-settings__actions {
      display: flex;
      gap: var(--spacing-3);
    }

    .alert {
      display: flex;
      align-items: center;
      gap: var(--spacing-3);
      padding: var(--spacing-4);
      border-radius: var(--border-radius-lg);
      margin-bottom: var(--spacing-4);
      font-weight: var(--font-weight-medium);
    }

    .alert-success {
      background: var(--color-green-50);
      color: var(--color-green-800);
      border: 1px solid var(--color-green-200);
    }

    .alert-error {
      background: var(--color-red-50);
      color: var(--color-red-800);
      border: 1px solid var(--color-red-200);
    }

    .loading-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: var(--spacing-12);
      text-align: center;
    }

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid var(--color-gray-200);
      border-top: 3px solid var(--color-primary-600);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: var(--spacing-4);
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .btn-spinner {
      width: 16px;
      height: 16px;
      border: 2px solid transparent;
      border-top: 2px solid currentColor;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: var(--spacing-2);
    }

    .settings-container {
      display: grid;
      grid-template-columns: 300px 1fr;
      gap: var(--spacing-6);
      min-height: 600px;
    }

    .settings-nav {
      background: var(--color-white);
      border: 1px solid var(--color-gray-200);
      border-radius: var(--border-radius-lg);
      padding: var(--spacing-4);
      height: fit-content;
      position: sticky;
      top: var(--spacing-4);
    }

    .settings-nav__title {
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-900);
      margin-bottom: var(--spacing-3);
      padding: 0 var(--spacing-2);
    }

    .settings-nav__list {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-1);
    }

    .settings-nav__item {
      display: flex;
      align-items: center;
      gap: var(--spacing-3);
      padding: var(--spacing-3);
      border: none;
      background: none;
      border-radius: var(--border-radius-md);
      cursor: pointer;
      transition: all 0.2s ease;
      text-align: left;
      width: 100%;
    }

    .settings-nav__item:hover {
      background: var(--color-gray-50);
    }

    .settings-nav__item--active {
      background: var(--color-primary-50);
      color: var(--color-primary-700);
    }

    .settings-nav__icon {
      width: 24px;
      height: 24px;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .settings-nav__name {
      font-weight: var(--font-weight-medium);
      font-size: var(--font-size-sm);
    }

    .settings-nav__description {
      font-size: var(--font-size-xs);
      color: var(--color-gray-500);
      margin-top: var(--spacing-1);
    }

    .settings-content {
      background: var(--color-white);
      border: 1px solid var(--color-gray-200);
      border-radius: var(--border-radius-lg);
      padding: var(--spacing-6);
    }

    .settings-section__header {
      margin-bottom: var(--spacing-6);
      padding-bottom: var(--spacing-4);
      border-bottom: 1px solid var(--color-gray-200);
    }

    .settings-section__title {
      font-size: var(--font-size-2xl);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-900);
      margin: 0 0 var(--spacing-2) 0;
    }

    .settings-section__description {
      color: var(--color-gray-600);
      margin: 0;
    }

    .settings-form {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-6);
    }

    .setting-item {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-3);
    }

    .setting-item__header {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-1);
    }

    .setting-item__label {
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-900);
      display: flex;
      align-items: center;
      gap: var(--spacing-1);
    }

    .required-indicator {
      color: var(--color-red-500);
    }

    .setting-item__description {
      font-size: var(--font-size-sm);
      color: var(--color-gray-600);
    }

    .setting-item__control {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-2);
    }

    .form-input, .form-select, .form-textarea {
      padding: var(--spacing-3);
      border: 1px solid var(--color-gray-300);
      border-radius: var(--border-radius-md);
      font-size: var(--font-size-sm);
      transition: border-color 0.2s ease;
      width: 100%;
      max-width: 400px;
    }

    .form-textarea {
      resize: vertical;
      font-family: monospace;
      max-width: 600px;
    }

    .form-input:focus, .form-select:focus, .form-textarea:focus {
      outline: none;
      border-color: var(--color-primary-500);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .toggle-switch {
      display: flex;
      align-items: center;
      gap: var(--spacing-3);
    }

    .toggle-input {
      display: none;
    }

    .toggle-label {
      position: relative;
      width: 48px;
      height: 24px;
      background: var(--color-gray-300);
      border-radius: 12px;
      cursor: pointer;
      transition: background-color 0.2s ease;
    }

    .toggle-input:checked + .toggle-label {
      background: var(--color-primary-500);
    }

    .toggle-slider {
      position: absolute;
      top: 2px;
      left: 2px;
      width: 20px;
      height: 20px;
      background: white;
      border-radius: 50%;
      transition: transform 0.2s ease;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .toggle-input:checked + .toggle-label .toggle-slider {
      transform: translateX(24px);
    }

    .toggle-text {
      font-size: var(--font-size-sm);
      color: var(--color-gray-700);
      font-weight: var(--font-weight-medium);
    }

    .form-error {
      font-size: var(--font-size-xs);
      color: var(--color-red-600);
    }

    @media (max-width: 768px) {
      .system-settings {
        padding: var(--spacing-4);
      }

      .system-settings__header {
        flex-direction: column;
        align-items: stretch;
      }

      .settings-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
      }

      .settings-nav {
        position: static;
      }

      .settings-nav__list {
        flex-direction: row;
        overflow-x: auto;
        gap: var(--spacing-2);
      }

      .settings-nav__item {
        flex-shrink: 0;
        min-width: 120px;
      }

      .form-input, .form-select, .form-textarea {
        max-width: 100%;
      }
    }
  `]
})
export class SystemSettingsComponent implements OnInit {
  private fb = inject(FormBuilder);

  // Data
  settingCategories: SettingCategory[] = [];
  selectedCategory = 'general';
  selectedCategoryData: SettingCategory | null = null;

  // State
  loading = false;
  saving = false;
  hasChanges = false;
  successMessage = '';
  errorMessage = '';

  // Form
  settingsForm!: FormGroup;
  originalValues: any = {};

  ngOnInit() {
    this.initializeSettings();
    this.loadSettings();
  }

  private initializeSettings() {
    this.settingCategories = [
      {
        name: 'general',
        displayName: 'General',
        description: 'Basic application settings',
        icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"></circle><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path></svg>',
        settings: [
          {
            key: 'app_name',
            value: 'Bug Tracker Pro',
            type: 'string',
            category: 'general',
            displayName: 'Application Name',
            description: 'The name displayed in the application header and browser title',
            required: true
          },
          {
            key: 'app_description',
            value: 'Professional bug tracking and project management system',
            type: 'string',
            category: 'general',
            displayName: 'Application Description',
            description: 'Brief description of the application'
          },
          {
            key: 'default_timezone',
            value: 'UTC',
            type: 'select',
            category: 'general',
            displayName: 'Default Timezone',
            description: 'Default timezone for new users and system operations',
            options: ['UTC', 'America/New_York', 'America/Los_Angeles', 'Europe/London', 'Europe/Berlin', 'Asia/Tokyo', 'Asia/Shanghai'],
            required: true
          },
          {
            key: 'maintenance_mode',
            value: false,
            type: 'boolean',
            category: 'general',
            displayName: 'Maintenance Mode',
            description: 'Enable maintenance mode to prevent user access during updates'
          }
        ]
      },
      {
        name: 'security',
        displayName: 'Security',
        description: 'Authentication and security settings',
        icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path></svg>',
        settings: [
          {
            key: 'session_timeout',
            value: 480,
            type: 'number',
            category: 'security',
            displayName: 'Session Timeout (minutes)',
            description: 'Automatic logout time for inactive users',
            required: true,
            validation: { min: 15, max: 1440 }
          },
          {
            key: 'password_min_length',
            value: 8,
            type: 'number',
            category: 'security',
            displayName: 'Minimum Password Length',
            description: 'Minimum number of characters required for passwords',
            required: true,
            validation: { min: 6, max: 50 }
          },
          {
            key: 'require_password_complexity',
            value: true,
            type: 'boolean',
            category: 'security',
            displayName: 'Require Password Complexity',
            description: 'Require passwords to contain uppercase, lowercase, numbers, and special characters'
          },
          {
            key: 'max_login_attempts',
            value: 5,
            type: 'number',
            category: 'security',
            displayName: 'Max Login Attempts',
            description: 'Maximum failed login attempts before account lockout',
            required: true,
            validation: { min: 3, max: 20 }
          }
        ]
      },
      {
        name: 'notifications',
        displayName: 'Notifications',
        description: 'Email and notification settings',
        icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path><path d="M13.73 21a2 2 0 0 1-3.46 0"></path></svg>',
        settings: [
          {
            key: 'smtp_host',
            value: '',
            type: 'string',
            category: 'notifications',
            displayName: 'SMTP Host',
            description: 'SMTP server hostname for sending emails'
          },
          {
            key: 'smtp_port',
            value: 587,
            type: 'number',
            category: 'notifications',
            displayName: 'SMTP Port',
            description: 'SMTP server port number',
            validation: { min: 1, max: 65535 }
          },
          {
            key: 'smtp_username',
            value: '',
            type: 'string',
            category: 'notifications',
            displayName: 'SMTP Username',
            description: 'Username for SMTP authentication'
          },
          {
            key: 'email_notifications_enabled',
            value: true,
            type: 'boolean',
            category: 'notifications',
            displayName: 'Enable Email Notifications',
            description: 'Allow the system to send email notifications to users'
          },
          {
            key: 'notification_from_email',
            value: '<EMAIL>',
            type: 'string',
            category: 'notifications',
            displayName: 'From Email Address',
            description: 'Email address used as sender for system notifications'
          }
        ]
      },
      {
        name: 'integrations',
        displayName: 'Integrations',
        description: 'Third-party service integrations',
        icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"></path><path d="M16 6l-4-2-4 2"></path><path d="M8 6v6"></path><path d="M16 6v6"></path></svg>',
        settings: [
          {
            key: 'slack_webhook_url',
            value: '',
            type: 'string',
            category: 'integrations',
            displayName: 'Slack Webhook URL',
            description: 'Webhook URL for Slack notifications'
          },
          {
            key: 'teams_webhook_url',
            value: '',
            type: 'string',
            category: 'integrations',
            displayName: 'Microsoft Teams Webhook URL',
            description: 'Webhook URL for Microsoft Teams notifications'
          },
          {
            key: 'jira_integration_enabled',
            value: false,
            type: 'boolean',
            category: 'integrations',
            displayName: 'Enable JIRA Integration',
            description: 'Enable synchronization with JIRA for bug tracking'
          },
          {
            key: 'jira_config',
            value: '{\n  "baseUrl": "",\n  "username": "",\n  "apiToken": "",\n  "projectKey": ""\n}',
            type: 'json',
            category: 'integrations',
            displayName: 'JIRA Configuration',
            description: 'JSON configuration for JIRA integration'
          }
        ]
      }
    ];

    this.selectCategory(this.selectedCategory);
  }

  private loadSettings() {
    this.loading = true;

    // Simulate API call
    setTimeout(() => {
      this.buildForm();
      this.loading = false;
    }, 500);
  }

  private buildForm() {
    if (!this.selectedCategoryData) return;

    const formControls: any = {};

    this.selectedCategoryData.settings.forEach(setting => {
      const validators = [];

      if (setting.required) {
        validators.push(Validators.required);
      }

      if (setting.type === 'number' && setting.validation) {
        if (setting.validation.min !== undefined) {
          validators.push(Validators.min(setting.validation.min));
        }
        if (setting.validation.max !== undefined) {
          validators.push(Validators.max(setting.validation.max));
        }
      }

      if (setting.type === 'json') {
        validators.push(this.jsonValidator);
      }

      formControls[setting.key] = [setting.value, validators];
      this.originalValues[setting.key] = setting.value;
    });

    this.settingsForm = this.fb.group(formControls);

    // Watch for changes
    this.settingsForm.valueChanges.subscribe(() => {
      this.checkForChanges();
    });
  }

  private jsonValidator(control: any) {
    if (!control.value) return null;

    try {
      JSON.parse(control.value);
      return null;
    } catch (e) {
      return { invalidJson: true };
    }
  }

  private checkForChanges() {
    if (!this.settingsForm) return;

    const currentValues = this.settingsForm.value;
    this.hasChanges = Object.keys(currentValues).some(
      key => currentValues[key] !== this.originalValues[key]
    );
  }

  selectCategory(categoryName: string) {
    this.selectedCategory = categoryName;
    this.selectedCategoryData = this.settingCategories.find(cat => cat.name === categoryName) || null;
    this.buildForm();
    this.clearMessages();
  }

  saveSettings() {
    if (!this.settingsForm || this.settingsForm.invalid) return;

    this.saving = true;
    this.clearMessages();

    // Simulate API call
    setTimeout(() => {
      try {
        // Update the original values
        const formValues = this.settingsForm.value;
        Object.keys(formValues).forEach(key => {
          this.originalValues[key] = formValues[key];

          // Update the setting value in the category data
          if (this.selectedCategoryData) {
            const setting = this.selectedCategoryData.settings.find(s => s.key === key);
            if (setting) {
              setting.value = formValues[key];
            }
          }
        });

        this.hasChanges = false;
        this.successMessage = 'Settings saved successfully!';
        this.saving = false;

        // Clear success message after 3 seconds
        setTimeout(() => {
          this.successMessage = '';
        }, 3000);
      } catch (error) {
        this.errorMessage = 'Failed to save settings. Please try again.';
        this.saving = false;
      }
    }, 1000);
  }

  resetToDefaults() {
    if (!confirm('Are you sure you want to reset all settings to their default values? This action cannot be undone.')) {
      return;
    }

    // Reset to default values (this would typically come from the API)
    this.initializeSettings();
    this.selectCategory(this.selectedCategory);
    this.successMessage = 'Settings reset to defaults successfully!';

    setTimeout(() => {
      this.successMessage = '';
    }, 3000);
  }

  private clearMessages() {
    this.successMessage = '';
    this.errorMessage = '';
  }

  // Utility methods
  trackByCategory(index: number, category: SettingCategory): string {
    return category.name;
  }

  trackBySetting(index: number, setting: SystemSetting): string {
    return setting.key;
  }
}
