// Common API response interfaces
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: ApiError[];
  timestamp: Date;
}

export interface ApiError {
  field?: string;
  code: string;
  message: string;
}

export interface PaginatedResponse<T = any> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

export interface PaginationParams {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface SortOption {
  field: string;
  label: string;
  direction?: 'asc' | 'desc';
}

// Dashboard metrics interfaces
export interface DashboardMetrics {
  applications: ApplicationMetrics;
  bugs: BugMetrics;
  userMetrics?: UserSpecificMetrics;
  charts: ChartData[];
  recentActivity: ActivityItem[];
}

export interface ApplicationMetrics {
  totalRegistered: number;
  qaInProgress: number;
  qaCompleted: number;
  activeProjects: number;
}

export interface BugMetrics {
  totalRaised: number;
  totalFixed: number;
  totalPending: number;
  criticalBugs: number;
  overdueBugs: number;
  averageResolutionTime: number; // in hours
}

export interface UserSpecificMetrics {
  userId: string;
  projectsAssigned: number;
  bugsAssigned: number;
  bugsFixed: number;
  bugsPending: number;
  fixRate: number; // percentage
  reopenRate: number; // percentage
}

export interface ChartData {
  id: string;
  title: string;
  type: 'line' | 'bar' | 'pie' | 'doughnut';
  data: ChartDataPoint[];
  labels?: string[];
  colors?: string[];
}

export interface ChartDataPoint {
  label: string;
  value: number;
  color?: string;
  metadata?: any;
}

export interface ActivityItem {
  id: string;
  type: ActivityType;
  title: string;
  description: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  timestamp: Date;
  metadata?: any;
}

export enum ActivityType {
  BUG_CREATED = 'bug_created',
  BUG_UPDATED = 'bug_updated',
  BUG_ASSIGNED = 'bug_assigned',
  BUG_RESOLVED = 'bug_resolved',
  BUG_CLOSED = 'bug_closed',
  BUG_REOPENED = 'bug_reopened',
  PROJECT_CREATED = 'project_created',
  PROJECT_UPDATED = 'project_updated',
  USER_JOINED = 'user_joined',
  COMMENT_ADDED = 'comment_added'
}

// Notification interfaces
export interface Notification {
  id: string;
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: any;
  isRead: boolean;
  createdAt: Date;
  readAt?: Date;
}

export enum NotificationType {
  BUG_ASSIGNED = 'bug_assigned',
  BUG_STATUS_CHANGED = 'bug_status_changed',
  BUG_COMMENT = 'bug_comment',
  BUG_OVERDUE = 'bug_overdue',
  PROJECT_ASSIGNED = 'project_assigned',
  SYSTEM_ANNOUNCEMENT = 'system_announcement'
}

// File upload interfaces
export interface FileUploadResponse {
  id: string;
  fileName: string;
  originalFileName: string;
  fileSize: number;
  mimeType: string;
  downloadUrl: string;
  thumbnailUrl?: string;
}

export interface FileUploadProgress {
  fileName: string;
  progress: number;
  status: 'uploading' | 'completed' | 'error';
  error?: string;
}

// Search interfaces
export interface SearchResult<T = any> {
  type: SearchResultType;
  id: string;
  title: string;
  description?: string;
  data: T;
  relevanceScore: number;
}

export enum SearchResultType {
  BUG = 'bug',
  PROJECT = 'project',
  USER = 'user',
  COMMENT = 'comment'
}

export interface SearchRequest {
  query: string;
  types?: SearchResultType[];
  filters?: any;
  limit?: number;
}

// Export interfaces
export interface ExportRequest {
  format: ExportFormat;
  filters?: any;
  columns?: string[];
  includeAttachments?: boolean;
}

export enum ExportFormat {
  CSV = 'csv',
  EXCEL = 'excel',
  PDF = 'pdf',
  JSON = 'json'
}

export interface ExportResponse {
  id: string;
  fileName: string;
  downloadUrl: string;
  expiresAt: Date;
}

// Settings interfaces
export interface SystemSettings {
  general: GeneralSettings;
  email: EmailSettings;
  security: SecuritySettings;
  integrations: IntegrationSettings;
}

export interface GeneralSettings {
  siteName: string;
  siteUrl: string;
  defaultTimezone: string;
  defaultLanguage: string;
  maintenanceMode: boolean;
  allowUserRegistration: boolean;
}

export interface EmailSettings {
  smtpHost: string;
  smtpPort: number;
  smtpUsername: string;
  smtpPassword: string;
  fromEmail: string;
  fromName: string;
  enableEmailNotifications: boolean;
}

export interface SecuritySettings {
  passwordMinLength: number;
  passwordRequireUppercase: boolean;
  passwordRequireLowercase: boolean;
  passwordRequireNumbers: boolean;
  passwordRequireSymbols: boolean;
  sessionTimeout: number; // in minutes
  maxLoginAttempts: number;
  lockoutDuration: number; // in minutes
}

export interface IntegrationSettings {
  teamsWebhookUrl?: string;
  slackWebhookUrl?: string;
  azureDevOpsUrl?: string;
  azureDevOpsToken?: string;
  enableTeamsNotifications: boolean;
  enableSlackNotifications: boolean;
  enableAzureDevOpsSync: boolean;
}

// Utility types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

// Constants
export const DEFAULT_PAGE_SIZE = 20;
export const MAX_PAGE_SIZE = 100;
export const DEFAULT_SORT_ORDER = 'desc';

export const SUPPORTED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/gif',
  'image/webp'
];

export const SUPPORTED_DOCUMENT_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'text/plain',
  'text/csv'
];

export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

// Helper functions
export function isImageFile(mimeType: string): boolean {
  return SUPPORTED_IMAGE_TYPES.includes(mimeType);
}

export function isDocumentFile(mimeType: string): boolean {
  return SUPPORTED_DOCUMENT_TYPES.includes(mimeType);
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

export function generateId(): string {
  return Math.random().toString(36).substr(2, 9);
}

export function formatDate(date: Date | string): string {
  const d = new Date(date);
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}

export function formatDateTime(date: Date | string): string {
  const d = new Date(date);
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}

export function getRelativeTime(date: Date | string): string {
  const now = new Date();
  const target = new Date(date);
  const diffMs = now.getTime() - target.getTime();
  const diffMins = Math.floor(diffMs / 60000);
  const diffHours = Math.floor(diffMins / 60);
  const diffDays = Math.floor(diffHours / 24);

  if (diffMins < 1) return 'just now';
  if (diffMins < 60) return `${diffMins}m ago`;
  if (diffHours < 24) return `${diffHours}h ago`;
  if (diffDays < 7) return `${diffDays}d ago`;
  
  return formatDate(date);
}
