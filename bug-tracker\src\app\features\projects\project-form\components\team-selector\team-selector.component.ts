import { Component, Input, Output, EventEmitter, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { UserService } from '../../../../../core/services/user.service';
import { AuthService } from '../../../../../core/services/auth.service';
import { User, UserRole } from '../../../../../core/models/user.model';
import { CreateTeamMemberRequest } from '../../../../../core/models/project.model';

export interface TeamMemberSelection {
  userId: string;
  user: User;
  role: UserRole;
}

@Component({
  selector: 'app-team-selector',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="team-selector">
      <div class="team-selector__header">
        <h3>Team Members</h3>
        <p>Assign team members to this project by role</p>
      </div>

      <!-- Role and User Selection -->
      <div class="team-selection-form">
        <div class="selection-row">
          <div class="form-group">
            <label class="form-label">Select Role</label>
            <select 
              class="form-control" 
              [(ngModel)]="selectedRole" 
              (change)="onRoleChange()">
              <option value="">Choose a role...</option>
              <option *ngFor="let role of availableRoles" [value]="role">{{ role }}</option>
            </select>
          </div>

          <div class="form-group" *ngIf="selectedRole">
            <label class="form-label">Select Users</label>
            <select 
              class="form-control" 
              [(ngModel)]="selectedUserId" 
              (change)="onUserChange()">
              <option value="">Choose a user...</option>
              <option
                *ngFor="let user of availableUsers"
                [value]="user.id"
                [disabled]="isUserAlreadySelected(user.id)">
                {{ user.fullName }} ({{ user.email }}){{ isUserAlreadySelected(user.id) ? ' - Already selected' : '' }}
              </option>
            </select>
          </div>

          <div class="form-group" *ngIf="selectedRole && selectedUserId">
            <label class="form-label">&nbsp;</label>
            <button 
              type="button" 
              class="btn btn-primary" 
              (click)="addTeamMember()"
              [disabled]="!canAddUser()">
              Add to Team
            </button>
          </div>
        </div>
      </div>

      <!-- Selected Team Members -->
      <div class="selected-team" *ngIf="selectedTeamMembers.length > 0">
        <h4>Selected Team Members ({{ selectedTeamMembers.length }})</h4>
        
        <div class="team-by-role" *ngFor="let roleGroup of teamMembersByRole">
          <div class="role-section">
            <h5>{{ roleGroup.role }} ({{ roleGroup.members.length }})</h5>
            <div class="team-members-list">
              <div 
                class="team-member-item" 
                *ngFor="let member of roleGroup.members">
                <div class="member-info">
                  <div class="member-avatar">{{ getInitials(member.user.fullName) }}</div>
                  <div class="member-details">
                    <span class="member-name">{{ member.user.fullName }}</span>
                    <span class="member-email">{{ member.user.email }}</span>
                  </div>
                </div>
                <button 
                  type="button" 
                  class="btn-icon btn-icon--danger" 
                  (click)="removeTeamMember(member.userId)"
                  title="Remove from team">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div class="empty-team-state" *ngIf="selectedTeamMembers.length === 0">
        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1">
          <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
          <circle cx="9" cy="7" r="4"></circle>
          <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
          <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
        </svg>
        <p>No team members selected yet. Choose a role and add users to get started.</p>
      </div>
    </div>
  `,
  styleUrl: './team-selector.component.scss'
})
export class TeamSelectorComponent implements OnInit {
  @Input() selectedTeamMembers: TeamMemberSelection[] = [];
  @Output() teamMembersChange = new EventEmitter<TeamMemberSelection[]>();

  private userService = inject(UserService);
  private authService = inject(AuthService);

  allUsers: User[] = [];
  availableRoles: UserRole[] = [];
  availableUsers: User[] = [];
  selectedRole: UserRole | '' = '';
  selectedUserId = '';

  ngOnInit() {
    this.loadUsers();
    this.initializeAvailableRoles();
  }

  get teamMembersByRole() {
    const roleGroups: { role: UserRole; members: TeamMemberSelection[] }[] = [];
    
    this.availableRoles.forEach(role => {
      const members = this.selectedTeamMembers.filter(member => member.role === role);
      if (members.length > 0) {
        roleGroups.push({ role, members });
      }
    });
    
    return roleGroups;
  }

  private loadUsers() {
    this.userService.getUsers().subscribe({
      next: (users) => {
        this.allUsers = users.filter(user => user.isActive);
      },
      error: (error) => {
        console.error('Error loading users:', error);
      }
    });
  }

  private initializeAvailableRoles() {
    const currentUser = this.authService.getCurrentUser();
    
    if (currentUser?.role === UserRole.ADMIN) {
      // Admins can assign all roles
      this.availableRoles = Object.values(UserRole);
    } else if (currentUser?.role === UserRole.PROJECT_MANAGER) {
      // Project managers can assign most roles except Admin
      this.availableRoles = [
        UserRole.PROJECT_MANAGER,
        UserRole.DEVELOPER,
        UserRole.QA_ENGINEER,
        UserRole.BUSINESS_ANALYST
      ];
    } else {
      // Other roles have limited assignment capabilities
      this.availableRoles = [
        UserRole.DEVELOPER,
        UserRole.QA_ENGINEER,
        UserRole.BUSINESS_ANALYST
      ];
    }
  }

  onRoleChange() {
    this.selectedUserId = '';
    if (this.selectedRole) {
      this.availableUsers = this.allUsers.filter(user => user.role === this.selectedRole);
    } else {
      this.availableUsers = [];
    }
  }

  onUserChange() {
    // User selection handled in template
  }

  addTeamMember() {
    if (this.canAddUser()) {
      const user = this.allUsers.find(u => u.id === this.selectedUserId);
      if (user && this.selectedRole) {
        const newMember: TeamMemberSelection = {
          userId: user.id,
          user: user,
          role: this.selectedRole as UserRole
        };
        
        const updatedMembers = [...this.selectedTeamMembers, newMember];
        this.selectedTeamMembers = updatedMembers;
        this.teamMembersChange.emit(updatedMembers);
        
        // Reset selection
        this.selectedUserId = '';
      }
    }
  }

  removeTeamMember(userId: string) {
    const updatedMembers = this.selectedTeamMembers.filter(member => member.userId !== userId);
    this.selectedTeamMembers = updatedMembers;
    this.teamMembersChange.emit(updatedMembers);
  }

  isUserAlreadySelected(userId: string): boolean {
    return this.selectedTeamMembers.some(member => member.userId === userId);
  }

  canAddUser(): boolean {
    return !!(this.selectedRole && this.selectedUserId && !this.isUserAlreadySelected(this.selectedUserId));
  }

  getInitials(fullName: string): string {
    return fullName.split(' ').map(name => name[0]).join('').toUpperCase().slice(0, 2);
  }
}
