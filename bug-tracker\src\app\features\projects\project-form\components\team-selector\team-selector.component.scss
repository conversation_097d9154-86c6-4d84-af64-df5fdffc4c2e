.team-selector {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
}

.team-selector__header {
  h3 {
    margin: 0 0 var(--spacing-2) 0;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--color-gray-900);
  }

  p {
    margin: 0;
    color: var(--color-gray-600);
    font-size: var(--font-size-sm);
  }
}

.team-selection-form {
  background: var(--color-gray-50);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-4);
}

.selection-row {
  display: grid;
  grid-template-columns: 1fr 1fr auto;
  gap: var(--spacing-4);
  align-items: end;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.form-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
}

.form-control {
  padding: var(--spacing-3);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);
  background: var(--color-white);

  &:focus {
    outline: none;
    border-color: var(--color-primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  option:disabled {
    color: var(--color-gray-400);
    font-style: italic;
  }
}

.selected-team {
  h4 {
    margin: 0 0 var(--spacing-4) 0;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--color-gray-900);
  }
}

.team-by-role {
  margin-bottom: var(--spacing-6);

  &:last-child {
    margin-bottom: 0;
  }
}

.role-section {
  h5 {
    margin: 0 0 var(--spacing-3) 0;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--color-primary-700);
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
}

.team-members-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.team-member-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--color-white);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-3);
  transition: all var(--transition-fast);

  &:hover {
    border-color: var(--color-gray-300);
    box-shadow: var(--shadow-sm);
  }
}

.member-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  flex: 1;
}

.member-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--color-primary-100);
  color: var(--color-primary-700);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  flex-shrink: 0;
}

.member-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.member-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-900);
}

.member-email {
  font-size: var(--font-size-xs);
  color: var(--color-gray-600);
}

.btn-icon {
  background: none;
  border: none;
  padding: var(--spacing-2);
  cursor: pointer;
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: var(--color-gray-100);
  }

  &--danger {
    color: var(--color-error-600);

    &:hover {
      background: var(--color-error-50);
      color: var(--color-error-700);
    }
  }
}

.empty-team-state {
  text-align: center;
  padding: var(--spacing-8) var(--spacing-4);
  color: var(--color-gray-500);

  svg {
    margin-bottom: var(--spacing-4);
    opacity: 0.6;
  }

  p {
    margin: 0;
    font-size: var(--font-size-sm);
  }
}

.btn {
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  border: 1px solid transparent;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.btn-primary {
  background: var(--color-primary-600);
  color: var(--color-white);
  border-color: var(--color-primary-600);

  &:hover:not(:disabled) {
    background: var(--color-primary-700);
    border-color: var(--color-primary-700);
  }
}

// Responsive design
@media (max-width: 768px) {
  .selection-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-3);
  }

  .team-member-item {
    padding: var(--spacing-2);
  }

  .member-avatar {
    width: 32px;
    height: 32px;
    font-size: var(--font-size-xs);
  }

  .member-details {
    gap: 0;
  }
}

@media (max-width: 480px) {
  .member-info {
    gap: var(--spacing-2);
  }

  .member-details .member-email {
    display: none;
  }
}
