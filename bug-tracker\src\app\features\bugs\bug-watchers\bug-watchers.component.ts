import { Component, Input, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { BugService } from '../../../core/services/bug.service';
import { UserService } from '../../../core/services/user.service';
import { AuthService } from '../../../core/services/auth.service';
import { BugWatcher, WatcherRequest, WatcherNotificationPreferences } from '../../../core/models/bug.model';
import { User, AuthUser, getUserInitials, UserRole } from '../../../core/models/user.model';

@Component({
  selector: 'app-bug-watchers',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  template: `
    <div class="bug-watchers">
      <!-- Watchers Header -->
      <div class="watchers-header">
        <h3 class="watchers-title">
          Watchers
          <span class="watchers-count" *ngIf="watchers.length > 0">({{ watchers.length }})</span>
        </h3>
        <button 
          class="btn btn-sm btn-primary"
          (click)="showAddWatcherModal = true"
          *ngIf="canManageWatchers()"
          title="Add watcher"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="3"/>
            <path d="M12 1v6m0 6v6"/>
            <path d="M1 12h6m6 0h6"/>
          </svg>
          Add Watcher
        </button>
      </div>

      <!-- Current User Watch Status -->
      <div class="current-user-status" *ngIf="currentUser">
        <div class="status-card" [class.watching]="isCurrentUserWatching">
          <div class="status-info">
            <div class="status-icon">
              <svg *ngIf="isCurrentUserWatching" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                <circle cx="12" cy="12" r="3"/>
              </svg>
              <svg *ngIf="!isCurrentUserWatching" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                <line x1="1" y1="1" x2="23" y2="23"/>
              </svg>
            </div>
            <div class="status-text">
              <span class="status-label">{{ isCurrentUserWatching ? 'You are watching this bug' : 'You are not watching this bug' }}</span>
              <span class="status-description" *ngIf="isCurrentUserWatching">You will receive notifications about updates</span>
            </div>
          </div>
          <div class="status-actions">
            <button 
              class="btn btn-sm"
              [class.btn-secondary]="isCurrentUserWatching"
              [class.btn-primary]="!isCurrentUserWatching"
              (click)="toggleCurrentUserWatch()"
              [disabled]="updating"
            >
              <span *ngIf="updating" class="btn-spinner"></span>
              {{ isCurrentUserWatching ? 'Stop Watching' : 'Watch' }}
            </button>
            <button 
              class="btn btn-sm btn-ghost"
              (click)="showPreferencesModal = true"
              *ngIf="isCurrentUserWatching"
              title="Notification preferences"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="3"/>
                <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- Watchers List -->
      <div class="watchers-list" *ngIf="watchers.length > 0">
        <div 
          class="watcher-item"
          *ngFor="let watcher of watchers; trackBy: trackByWatcherId"
        >
          <div class="watcher-avatar">
            <div class="avatar-circle">{{ getUserInitials(watcher.user) }}</div>
          </div>
          
          <div class="watcher-info">
            <div class="watcher-name">{{ watcher.user.fullName }}</div>
            <div class="watcher-meta">
              <span class="watcher-role">{{ watcher.user.role }}</span>
              <span class="watcher-since">Watching since {{ formatDate(watcher.watchedAt) }}</span>
            </div>
            <div class="watcher-preferences" *ngIf="showWatcherPreferences(watcher)">
              <div class="preferences-summary">
                <span class="preference-item" *ngIf="watcher.notificationPreferences.emailNotifications">
                  <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                    <polyline points="22,6 12,13 2,6"/>
                  </svg>
                  Email
                </span>
                <span class="preference-item" *ngIf="watcher.notificationPreferences.inAppNotifications">
                  <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"/>
                    <path d="M13.73 21a2 2 0 0 1-3.46 0"/>
                  </svg>
                  In-app
                </span>
              </div>
            </div>
          </div>

          <div class="watcher-actions" *ngIf="canRemoveWatcher(watcher)">
            <button 
              class="action-btn remove"
              (click)="removeWatcher(watcher)"
              title="Remove watcher"
            >
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="18" y1="6" x2="6" y2="18"/>
                <line x1="6" y1="6" x2="18" y2="18"/>
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div class="watchers-empty" *ngIf="watchers.length === 0">
        <div class="empty-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
            <circle cx="12" cy="12" r="3"/>
          </svg>
        </div>
        <h4 class="empty-title">No watchers yet</h4>
        <p class="empty-description">Add watchers to notify team members about bug updates.</p>
        <button 
          class="btn btn-primary"
          (click)="showAddWatcherModal = true"
          *ngIf="canManageWatchers()"
        >
          Add First Watcher
        </button>
      </div>

      <!-- Loading State -->
      <div class="watchers-loading" *ngIf="loading">
        <div class="loading-spinner"></div>
        <span>Loading watchers...</span>
      </div>

      <!-- Error State -->
      <div class="watchers-error" *ngIf="errorMessage">
        <div class="error-content">
          <svg class="error-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="10"/>
            <line x1="15" y1="9" x2="9" y2="15"/>
            <line x1="9" y1="9" x2="15" y2="15"/>
          </svg>
          <span>{{ errorMessage }}</span>
        </div>
        <button class="btn btn-sm btn-secondary" (click)="retryLoad()">
          Try Again
        </button>
      </div>
    </div>

    <!-- Add Watcher Modal -->
    <div *ngIf="showAddWatcherModal" class="modal-overlay" (click)="closeAddWatcherModal()">
      <div class="modal-content" (click)="$event.stopPropagation()">
        <div class="modal-header">
          <h3>Add Watcher</h3>
          <button class="modal-close" (click)="closeAddWatcherModal()">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="18" y1="6" x2="6" y2="18"/>
              <line x1="6" y1="6" x2="18" y2="18"/>
            </svg>
          </button>
        </div>

        <form [formGroup]="addWatcherForm" (ngSubmit)="addWatcher()" class="modal-body">
          <div class="form-group">
            <label for="userId">Select User</label>
            <select 
              id="userId" 
              formControlName="userId" 
              class="form-control"
              [class.error]="isFieldInvalid('userId')"
            >
              <option value="">Choose a user...</option>
              <option 
                *ngFor="let user of availableUsers" 
                [value]="user.id"
              >
                {{ user.fullName }} ({{ user.role }})
              </option>
            </select>
            <div *ngIf="isFieldInvalid('userId')" class="form-error">
              <span *ngIf="addWatcherForm.get('userId')?.errors?.['required']">Please select a user</span>
            </div>
          </div>

          <div class="form-group">
            <label class="form-label">Notification Preferences</label>
            <div class="checkbox-group">
              <label class="checkbox-label">
                <input type="checkbox" formControlName="emailNotifications" class="checkbox-input">
                <span class="checkbox-text">Email notifications</span>
              </label>
              <label class="checkbox-label">
                <input type="checkbox" formControlName="inAppNotifications" class="checkbox-input">
                <span class="checkbox-text">In-app notifications</span>
              </label>
              <label class="checkbox-label">
                <input type="checkbox" formControlName="onStatusChange" class="checkbox-input">
                <span class="checkbox-text">Status changes</span>
              </label>
              <label class="checkbox-label">
                <input type="checkbox" formControlName="onComment" class="checkbox-input">
                <span class="checkbox-text">New comments</span>
              </label>
              <label class="checkbox-label">
                <input type="checkbox" formControlName="onAssignment" class="checkbox-input">
                <span class="checkbox-text">Assignment changes</span>
              </label>
            </div>
          </div>

          <div class="modal-actions">
            <button type="button" class="btn btn-secondary" (click)="closeAddWatcherModal()">
              Cancel
            </button>
            <button 
              type="submit" 
              class="btn btn-primary"
              [disabled]="addWatcherForm.invalid || submitting"
            >
              <span *ngIf="submitting" class="btn-spinner"></span>
              Add Watcher
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Preferences Modal -->
    <div *ngIf="showPreferencesModal" class="modal-overlay" (click)="closePreferencesModal()">
      <div class="modal-content" (click)="$event.stopPropagation()">
        <div class="modal-header">
          <h3>Notification Preferences</h3>
          <button class="modal-close" (click)="closePreferencesModal()">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="18" y1="6" x2="6" y2="18"/>
              <line x1="6" y1="6" x2="18" y2="18"/>
            </svg>
          </button>
        </div>

        <form [formGroup]="preferencesForm" (ngSubmit)="updatePreferences()" class="modal-body">
          <div class="form-group">
            <label class="form-label">Notification Methods</label>
            <div class="checkbox-group">
              <label class="checkbox-label">
                <input type="checkbox" formControlName="emailNotifications" class="checkbox-input">
                <span class="checkbox-text">Email notifications</span>
              </label>
              <label class="checkbox-label">
                <input type="checkbox" formControlName="inAppNotifications" class="checkbox-input">
                <span class="checkbox-text">In-app notifications</span>
              </label>
            </div>
          </div>

          <div class="form-group">
            <label class="form-label">Notify me when</label>
            <div class="checkbox-group">
              <label class="checkbox-label">
                <input type="checkbox" formControlName="onStatusChange" class="checkbox-input">
                <span class="checkbox-text">Status changes</span>
              </label>
              <label class="checkbox-label">
                <input type="checkbox" formControlName="onComment" class="checkbox-input">
                <span class="checkbox-text">New comments are added</span>
              </label>
              <label class="checkbox-label">
                <input type="checkbox" formControlName="onAssignment" class="checkbox-input">
                <span class="checkbox-text">Assignment changes</span>
              </label>
              <label class="checkbox-label">
                <input type="checkbox" formControlName="onAttachment" class="checkbox-input">
                <span class="checkbox-text">Attachments are added</span>
              </label>
              <label class="checkbox-label">
                <input type="checkbox" formControlName="onPriorityChange" class="checkbox-input">
                <span class="checkbox-text">Priority changes</span>
              </label>
              <label class="checkbox-label">
                <input type="checkbox" formControlName="onSeverityChange" class="checkbox-input">
                <span class="checkbox-text">Severity changes</span>
              </label>
            </div>
          </div>

          <div class="modal-actions">
            <button type="button" class="btn btn-secondary" (click)="closePreferencesModal()">
              Cancel
            </button>
            <button 
              type="submit" 
              class="btn btn-primary"
              [disabled]="preferencesForm.invalid || submitting"
            >
              <span *ngIf="submitting" class="btn-spinner"></span>
              Save Preferences
            </button>
          </div>
        </form>
      </div>
    </div>
  `,
  styles: [`
    .bug-watchers {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-4);
    }

    .watchers-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: var(--spacing-3);
      border-bottom: 2px solid var(--color-primary-100);
    }

    .watchers-title {
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-900);
      margin: 0;
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
    }

    .watchers-count {
      font-size: var(--font-size-sm);
      color: var(--color-gray-500);
      font-weight: var(--font-weight-normal);
    }

    /* Current User Status */
    .current-user-status {
      margin-bottom: var(--spacing-4);
    }

    .status-card {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--spacing-4);
      background: var(--color-white);
      border: 2px solid var(--color-gray-200);
      border-radius: var(--border-radius-lg);
      transition: border-color 0.2s ease;
    }

    .status-card.watching {
      border-color: var(--color-green-300);
      background: var(--color-green-50);
    }

    .status-info {
      display: flex;
      align-items: center;
      gap: var(--spacing-3);
    }

    .status-icon {
      color: var(--color-gray-500);
      flex-shrink: 0;
    }

    .status-card.watching .status-icon {
      color: var(--color-green-600);
    }

    .status-text {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-1);
    }

    .status-label {
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-900);
    }

    .status-description {
      font-size: var(--font-size-sm);
      color: var(--color-gray-600);
    }

    .status-actions {
      display: flex;
      gap: var(--spacing-2);
    }

    /* Watchers List */
    .watchers-list {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-3);
    }

    .watcher-item {
      display: flex;
      align-items: flex-start;
      gap: var(--spacing-3);
      padding: var(--spacing-3);
      background: var(--color-white);
      border: 1px solid var(--color-gray-200);
      border-radius: var(--border-radius-md);
      transition: border-color 0.2s ease;
    }

    .watcher-item:hover {
      border-color: var(--color-gray-300);
    }

    .watcher-avatar {
      flex-shrink: 0;
    }

    .avatar-circle {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: var(--color-primary-500);
      color: var(--color-white);
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: var(--font-weight-medium);
      font-size: var(--font-size-sm);
    }

    .watcher-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: var(--spacing-1);
    }

    .watcher-name {
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-900);
    }

    .watcher-meta {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      flex-wrap: wrap;
    }

    .watcher-role {
      font-size: var(--font-size-xs);
      color: var(--color-gray-500);
      background: var(--color-gray-100);
      padding: var(--spacing-1) var(--spacing-2);
      border-radius: var(--border-radius-sm);
    }

    .watcher-since {
      font-size: var(--font-size-xs);
      color: var(--color-gray-500);
    }

    .watcher-preferences {
      margin-top: var(--spacing-1);
    }

    .preferences-summary {
      display: flex;
      gap: var(--spacing-2);
      flex-wrap: wrap;
    }

    .preference-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-1);
      font-size: var(--font-size-xs);
      color: var(--color-gray-600);
      background: var(--color-gray-50);
      padding: var(--spacing-1) var(--spacing-2);
      border-radius: var(--border-radius-sm);
    }

    .preference-item svg {
      color: var(--color-gray-500);
    }

    .watcher-actions {
      display: flex;
      gap: var(--spacing-1);
      opacity: 0;
      transition: opacity 0.2s ease;
    }

    .watcher-item:hover .watcher-actions {
      opacity: 1;
    }

    .action-btn {
      background: none;
      border: none;
      color: var(--color-gray-400);
      cursor: pointer;
      padding: var(--spacing-1);
      border-radius: var(--border-radius-sm);
      transition: color 0.2s ease, background-color 0.2s ease;
    }

    .action-btn:hover {
      color: var(--color-gray-600);
      background: var(--color-gray-100);
    }

    .action-btn.remove:hover {
      color: var(--color-red-600);
      background: var(--color-red-50);
    }

    /* Empty State */
    .watchers-empty {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: var(--spacing-12);
      text-align: center;
      background: var(--color-white);
      border: 1px solid var(--color-gray-200);
      border-radius: var(--border-radius-lg);
    }

    .empty-icon {
      color: var(--color-gray-300);
      margin-bottom: var(--spacing-4);
    }

    .empty-title {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-900);
      margin: 0 0 var(--spacing-2) 0;
    }

    .empty-description {
      font-size: var(--font-size-sm);
      color: var(--color-gray-500);
      margin: 0 0 var(--spacing-4) 0;
    }

    /* Loading State */
    .watchers-loading {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--spacing-3);
      padding: var(--spacing-8);
      color: var(--color-gray-600);
    }

    .loading-spinner {
      width: 20px;
      height: 20px;
      border: 2px solid var(--color-gray-200);
      border-top: 2px solid var(--color-primary-500);
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    /* Error State */
    .watchers-error {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--spacing-4);
      background: var(--color-red-50);
      border: 1px solid var(--color-red-200);
      border-radius: var(--border-radius-md);
      color: var(--color-red-800);
    }

    .error-content {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
    }

    .error-icon {
      color: var(--color-red-500);
      flex-shrink: 0;
    }

    /* Modal Styles */
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
      padding: var(--spacing-4);
    }

    .modal-content {
      background: var(--color-white);
      border-radius: var(--border-radius-lg);
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
      max-width: 500px;
      width: 100%;
      max-height: 90vh;
      overflow-y: auto;
    }

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--spacing-6);
      border-bottom: 1px solid var(--color-gray-200);
    }

    .modal-header h3 {
      margin: 0;
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-900);
    }

    .modal-close {
      background: none;
      border: none;
      color: var(--color-gray-400);
      cursor: pointer;
      padding: var(--spacing-1);
      border-radius: var(--border-radius-sm);
      transition: color 0.2s ease, background-color 0.2s ease;
    }

    .modal-close:hover {
      color: var(--color-gray-600);
      background: var(--color-gray-100);
    }

    .modal-body {
      padding: var(--spacing-6);
    }

    .form-group {
      margin-bottom: var(--spacing-4);
    }

    .form-label {
      display: block;
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-700);
      margin-bottom: var(--spacing-2);
    }

    .form-control {
      width: 100%;
      padding: var(--spacing-3);
      border: 1px solid var(--color-gray-300);
      border-radius: var(--border-radius-md);
      font-size: var(--font-size-sm);
      transition: border-color 0.2s ease, box-shadow 0.2s ease;
    }

    .form-control:focus {
      outline: none;
      border-color: var(--color-primary-500);
      box-shadow: 0 0 0 3px var(--color-primary-100);
    }

    .form-control.error {
      border-color: var(--color-red-500);
    }

    .form-error {
      font-size: var(--font-size-xs);
      color: var(--color-red-600);
      margin-top: var(--spacing-1);
    }

    .checkbox-group {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-2);
    }

    .checkbox-label {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      font-size: var(--font-size-sm);
      color: var(--color-gray-700);
      cursor: pointer;
    }

    .checkbox-input {
      margin: 0;
    }

    .modal-actions {
      display: flex;
      justify-content: flex-end;
      gap: var(--spacing-3);
      margin-top: var(--spacing-6);
      padding-top: var(--spacing-4);
      border-top: 1px solid var(--color-gray-200);
    }

    .btn-spinner {
      display: inline-block;
      width: 14px;
      height: 14px;
      border: 2px solid transparent;
      border-top: 2px solid currentColor;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: var(--spacing-1);
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .status-card {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-3);
      }

      .status-actions {
        align-self: stretch;
      }

      .watcher-item {
        flex-direction: column;
        gap: var(--spacing-2);
      }

      .watcher-actions {
        opacity: 1;
        align-self: flex-end;
      }

      .modal-overlay {
        padding: var(--spacing-2);
      }

      .modal-content {
        max-height: 95vh;
      }

      .modal-header,
      .modal-body {
        padding: var(--spacing-4);
      }

      .modal-actions {
        flex-direction: column;
      }
    }
  `]
})
export class BugWatchersComponent implements OnInit {
  @Input() bugId!: string;
  @Input() watchers: BugWatcher[] = [];

  private fb = inject(FormBuilder);
  private bugService = inject(BugService);
  private userService = inject(UserService);
  private authService = inject(AuthService);

  // State
  loading = false;
  updating = false;
  submitting = false;
  errorMessage = '';
  
  // User data
  currentUser: AuthUser | null = null;
  allUsers: User[] = [];
  availableUsers: User[] = [];
  
  // Watch status
  isCurrentUserWatching = false;
  currentUserWatcher: BugWatcher | null = null;
  
  // Modals
  showAddWatcherModal = false;
  showPreferencesModal = false;
  
  // Forms
  addWatcherForm!: FormGroup;
  preferencesForm!: FormGroup;

  ngOnInit() {
    this.currentUser = this.authService.getCurrentUser();
    this.initializeForms();
    this.loadUsers();
    this.updateWatchStatus();
  }

  private initializeForms() {
    this.addWatcherForm = this.fb.group({
      userId: ['', [Validators.required]],
      emailNotifications: [true],
      inAppNotifications: [true],
      onStatusChange: [true],
      onComment: [true],
      onAssignment: [true],
      onAttachment: [false],
      onPriorityChange: [true],
      onSeverityChange: [true]
    });

    this.preferencesForm = this.fb.group({
      emailNotifications: [true],
      inAppNotifications: [true],
      onStatusChange: [true],
      onComment: [true],
      onAssignment: [true],
      onAttachment: [false],
      onPriorityChange: [true],
      onSeverityChange: [true]
    });
  }

  private loadUsers() {
    this.allUsers = this.userService.getAllUsers();
    this.updateAvailableUsers();
  }

  private updateAvailableUsers() {
    const watcherUserIds = this.watchers.map(w => w.userId);
    this.availableUsers = this.allUsers.filter(user => 
      !watcherUserIds.includes(user.id) && user.isActive
    );
  }

  private updateWatchStatus() {
    if (this.currentUser) {
      this.currentUserWatcher = this.watchers.find(w => w.userId === this.currentUser!.id) || null;
      this.isCurrentUserWatching = !!this.currentUserWatcher;
      
      if (this.currentUserWatcher) {
        this.preferencesForm.patchValue(this.currentUserWatcher.notificationPreferences);
      }
    }
    this.updateAvailableUsers();
  }

  // Permission methods
  canManageWatchers(): boolean {
    if (!this.currentUser) return false;
    return this.currentUser.role === UserRole.ADMIN ||
           this.currentUser.role === UserRole.PROJECT_MANAGER;
  }

  canRemoveWatcher(watcher: BugWatcher): boolean {
    if (!this.currentUser) return false;
    // Users can remove themselves, admins can remove anyone
    return watcher.userId === this.currentUser.id || this.currentUser.role === UserRole.ADMIN;
  }

  showWatcherPreferences(watcher: BugWatcher): boolean {
    // Only show preferences for current user or if user is admin
    return watcher.userId === this.currentUser?.id || this.currentUser?.role === UserRole.ADMIN;
  }

  // Watch actions
  toggleCurrentUserWatch() {
    if (!this.currentUser || !this.bugId) return;

    this.updating = true;
    this.errorMessage = '';

    if (this.isCurrentUserWatching) {
      this.bugService.removeWatcher(this.bugId, this.currentUser.id).subscribe({
        next: (response) => {
          if (response.success) {
            // Parent component should refresh watchers
            console.log('Stopped watching bug');
          } else {
            this.errorMessage = response.message || 'Failed to stop watching';
          }
          this.updating = false;
        },
        error: (error) => {
          this.errorMessage = error.message || 'Failed to stop watching';
          this.updating = false;
        }
      });
    } else {
      const watcherRequest: WatcherRequest = {
        userId: this.currentUser.id,
        notificationPreferences: this.preferencesForm.value
      };

      this.bugService.addWatcher(this.bugId, watcherRequest).subscribe({
        next: (response) => {
          if (response.success) {
            // Parent component should refresh watchers
            console.log('Started watching bug');
          } else {
            this.errorMessage = response.message || 'Failed to start watching';
          }
          this.updating = false;
        },
        error: (error) => {
          this.errorMessage = error.message || 'Failed to start watching';
          this.updating = false;
        }
      });
    }
  }

  // Modal actions
  closeAddWatcherModal() {
    this.showAddWatcherModal = false;
    this.addWatcherForm.reset({
      userId: '',
      emailNotifications: true,
      inAppNotifications: true,
      onStatusChange: true,
      onComment: true,
      onAssignment: true,
      onAttachment: false,
      onPriorityChange: true,
      onSeverityChange: true
    });
  }

  closePreferencesModal() {
    this.showPreferencesModal = false;
    if (this.currentUserWatcher) {
      this.preferencesForm.patchValue(this.currentUserWatcher.notificationPreferences);
    }
  }

  // Form actions
  addWatcher() {
    if (this.addWatcherForm.invalid || !this.bugId) return;

    this.submitting = true;
    this.errorMessage = '';

    const formValue = this.addWatcherForm.value;
    const watcherRequest: WatcherRequest = {
      userId: formValue.userId,
      notificationPreferences: {
        emailNotifications: formValue.emailNotifications,
        inAppNotifications: formValue.inAppNotifications,
        onStatusChange: formValue.onStatusChange,
        onComment: formValue.onComment,
        onAssignment: formValue.onAssignment,
        onAttachment: formValue.onAttachment,
        onPriorityChange: formValue.onPriorityChange,
        onSeverityChange: formValue.onSeverityChange
      }
    };

    this.bugService.addWatcher(this.bugId, watcherRequest).subscribe({
      next: (response) => {
        if (response.success) {
          this.closeAddWatcherModal();
          // Parent component should refresh watchers
          console.log('Watcher added successfully');
        } else {
          this.errorMessage = response.message || 'Failed to add watcher';
        }
        this.submitting = false;
      },
      error: (error) => {
        this.errorMessage = error.message || 'Failed to add watcher';
        this.submitting = false;
      }
    });
  }

  updatePreferences() {
    if (this.preferencesForm.invalid || !this.bugId || !this.currentUser) return;

    this.submitting = true;
    this.errorMessage = '';

    this.bugService.updateWatcherPreferences(this.bugId, this.currentUser.id, this.preferencesForm.value).subscribe({
      next: (response) => {
        if (response.success) {
          this.closePreferencesModal();
          // Parent component should refresh watchers
          console.log('Preferences updated successfully');
        } else {
          this.errorMessage = response.message || 'Failed to update preferences';
        }
        this.submitting = false;
      },
      error: (error) => {
        this.errorMessage = error.message || 'Failed to update preferences';
        this.submitting = false;
      }
    });
  }

  removeWatcher(watcher: BugWatcher) {
    if (!this.bugId) return;

    if (confirm(`Are you sure you want to remove ${watcher.user.fullName} as a watcher?`)) {
      this.bugService.removeWatcher(this.bugId, watcher.userId).subscribe({
        next: (response) => {
          if (response.success) {
            // Parent component should refresh watchers
            console.log('Watcher removed successfully');
          } else {
            this.errorMessage = response.message || 'Failed to remove watcher';
          }
        },
        error: (error) => {
          this.errorMessage = error.message || 'Failed to remove watcher';
        }
      });
    }
  }

  retryLoad() {
    this.errorMessage = '';
    // Parent component should handle retry
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.addWatcherForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  // Utility methods
  getUserInitials(user: User): string {
    return getUserInitials(user);
  }

  formatDate(date: Date): string {
    const now = new Date();
    const watchDate = new Date(date);
    const diffMs = now.getTime() - watchDate.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
    
    return watchDate.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: watchDate.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
    });
  }

  trackByWatcherId(index: number, watcher: BugWatcher): string {
    return watcher.id;
  }
}
