You are s senior angular frontend developer tasked with building a robust, end-to-end Bug Tracking Tool front-end in Angular.
Use the attached PRD as your single source of truth for requirements:\\

I’ll also upload a **sample dashboard image**—reference it strictly for styling (color scheme, layout neatness, typography), not for functionality.
Additionally, i have shared below, links to other design/component galleries—use them as **inspiration** for styling and UI components, but do not copy their behavior or data flows.

[https://mui.com/material-ui/getting-started/templates/dashboard/](https://mui.com/material-ui/getting-started/templates/dashboard/)

[https://demo.uifoundations.com/dashboard/analytics](https://demo.uifoundations.com/dashboard/analytics)

[https://kit.uifoundations.com/components](https://kit.uifoundations.com/components)

[https://demo.uifoundations.com/dashboard/charts](https://demo.uifoundations.com/dashboard/charts)

[https://dribbble.com/shots/25372101-Deliro-Delivery-Dashboard](https://dribbble.com/shots/25372101-Deliro-Delivery-Dashboard)

**Styling guidelines:**

* **No Tailwind**, **no Angular Material**—all CSS must be fully **custom**.
* Provide a **global stylesheet** (`styles.scss`) for base tokens and utilities: buttons, inputs, typography scales, color palette, spacing, cards, shadows, etc.
* All other CSS must be **component-scoped** (each component has its own `.component.scss`).
* **Separate files:** every component’s template (`.html`), style (`.scss`), and logic (`.ts`) must be in distinct files.
* **Modals & Sliding Panels:** Prefer `ngx-bootstrap` for modal/sliding-panel implementations. If its behavior doesn’t fit, build a **reusable custom modal component** that matches ngx-bootstrap’s API and supports `<ng-content>` projection of arbitrary child components.

We will implement module by module. **Do not write any code** until we agree on the module and you’ve asked any clarifying questions you need. After each module, you must:

1. Summarize exactly what you’ll build.
2. List any assumptions or questions.
3. Ask me to confirm “Proceed with ?”

Only once I reply “Proceed with ” should you scaffold or implement that module.

**Scope for Phase 1 (Front-end only):**

* Angular (latest LTS) app structure, routing, services, and components
* Custom-styled components via global and component stylesheets
* Consume stub/mock HTTP endpoints (real .NET Core APIs come later)
* Unit tests for components (Jasmine/Karma or your preferred Angular test framework)

**Suggested Module Breakdown (you can refine):**

1. **Project Registration**
2. **Dashboard & Metrics**
3. **Bug Reporting**
4. **Bug Detail & Workflow**
5. **Shared & Core** (Header, Sidebar, Loading, Modal)
6. **Routing & Guards**
7. **Unit Tests & Linting**

**Your First Tasks:**

1. Confirm you can access the PRD link, the sample dashboard image, and any design links.
2. Ask any high-level clarifying questions about front-end architecture, styling approach, or module scope.
3. Propose the first module to implement (e.g., “Project Registration”) and ask me: “Proceed with Project Registration?”
