import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON>t, <PERSON><PERSON><PERSON><PERSON>, inject, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { Chart, ChartConfiguration, ChartType, registerables } from 'chart.js';
import { BugService } from '../../../core/services/bug.service';
import { ProjectService } from '../../../core/services/project.service';
import { UserService } from '../../../core/services/user.service';
import { BugStatus, BugSeverity, BugPriority } from '../../../core/models/bug.model';
import { Project } from '../../../core/models/project.model';
import { User } from '../../../core/models/user.model';

Chart.register(...registerables);

interface BugReportData {
  totalBugs: number;
  openBugs: number;
  closedBugs: number;
  criticalBugs: number;
  highPriorityBugs: number;
  averageResolutionTime: number;
  statusDistribution: { [key: string]: number };
  severityDistribution: { [key: string]: number };
  priorityDistribution: { [key: string]: number };
  monthlyTrends: { month: string; created: number; resolved: number }[];
  projectDistribution: { projectName: string; bugCount: number }[];
  assigneeDistribution: { assigneeName: string; bugCount: number }[];
}

@Component({
  selector: 'app-bug-reports',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  template: `
    <div class="reports-container">
      <div class="reports-header">
        <h1 class="page-title">Bug Reports & Analytics</h1>
        <p class="page-subtitle">Comprehensive bug tracking and analysis dashboard</p>
      </div>

      <!-- Filters -->
      <div class="filters-section">
        <form [formGroup]="filtersForm" class="filters-form">
          <div class="filter-group">
            <label class="filter-label">Date Range</label>
            <div class="date-range">
              <input type="date" formControlName="startDate" class="form-control">
              <span class="date-separator">to</span>
              <input type="date" formControlName="endDate" class="form-control">
            </div>
          </div>

          <div class="filter-group">
            <label class="filter-label">Project</label>
            <select formControlName="projectId" class="form-control">
              <option value="">All Projects</option>
              <option *ngFor="let project of projects" [value]="project.id">
                {{ project.name }}
              </option>
            </select>
          </div>

          <div class="filter-group">
            <label class="filter-label">Assignee</label>
            <select formControlName="assigneeId" class="form-control">
              <option value="">All Assignees</option>
              <option *ngFor="let user of users" [value]="user.id">
                {{ user.firstName }} {{ user.lastName }}
              </option>
            </select>
          </div>

          <div class="filter-group">
            <label class="filter-label">Status</label>
            <select formControlName="status" class="form-control">
              <option value="">All Statuses</option>
              <option value="NEW">New</option>
              <option value="IN_PROGRESS">In Progress</option>
              <option value="RESOLVED">Resolved</option>
              <option value="CLOSED">Closed</option>
              <option value="REOPENED">Reopened</option>
            </select>
          </div>

          <div class="filter-actions">
            <button type="button" (click)="applyFilters()" class="btn btn-primary">
              Apply Filters
            </button>
            <button type="button" (click)="resetFilters()" class="btn btn-outline">
              Reset
            </button>
            <button type="button" (click)="exportReport()" class="btn btn-success">
              Export Report
            </button>
          </div>
        </form>
      </div>

      <!-- Loading State -->
      <div *ngIf="loading" class="loading-container">
        <div class="spinner"></div>
        <p>Loading report data...</p>
      </div>

      <!-- Report Content -->
      <div *ngIf="!loading && reportData" class="report-content">
        <!-- Summary Cards -->
        <div class="summary-cards">
          <div class="summary-card">
            <div class="card-icon total">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2L2 7L12 12L22 7L12 2Z"></path>
                <path d="M2 17L12 22L22 17"></path>
                <path d="M2 12L12 17L22 12"></path>
              </svg>
            </div>
            <div class="card-content">
              <h3>{{ reportData.totalBugs }}</h3>
              <p>Total Bugs</p>
            </div>
          </div>

          <div class="summary-card">
            <div class="card-icon open">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M12 6v6l4 2" stroke="white" stroke-width="2" fill="none"></path>
              </svg>
            </div>
            <div class="card-content">
              <h3>{{ reportData.openBugs }}</h3>
              <p>Open Bugs</p>
            </div>
          </div>

          <div class="summary-card">
            <div class="card-icon closed">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M9 12l2 2 4-4" stroke="white" stroke-width="2" fill="none"></path>
              </svg>
            </div>
            <div class="card-content">
              <h3>{{ reportData.closedBugs }}</h3>
              <p>Closed Bugs</p>
            </div>
          </div>

          <div class="summary-card">
            <div class="card-icon critical">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2L2 7L12 12L22 7L12 2Z"></path>
                <circle cx="12" cy="12" r="3" fill="white"></circle>
              </svg>
            </div>
            <div class="card-content">
              <h3>{{ reportData.criticalBugs }}</h3>
              <p>Critical Bugs</p>
            </div>
          </div>

          <div class="summary-card">
            <div class="card-icon resolution">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M12 6v6l4 2" stroke="white" stroke-width="2" fill="none"></path>
              </svg>
            </div>
            <div class="card-content">
              <h3>{{ reportData.averageResolutionTime }}d</h3>
              <p>Avg Resolution Time</p>
            </div>
          </div>
        </div>

        <!-- Charts Section -->
        <div class="charts-section">
          <!-- Status Distribution -->
          <div class="chart-card">
            <div class="chart-header">
              <h3>Bug Status Distribution</h3>
              <p>Current distribution of bugs by status</p>
            </div>
            <div class="chart-container">
              <canvas #statusChart></canvas>
            </div>
          </div>

          <!-- Severity Distribution -->
          <div class="chart-card">
            <div class="chart-header">
              <h3>Severity Distribution</h3>
              <p>Bugs categorized by severity level</p>
            </div>
            <div class="chart-container">
              <canvas #severityChart></canvas>
            </div>
          </div>

          <!-- Monthly Trends -->
          <div class="chart-card full-width">
            <div class="chart-header">
              <h3>Monthly Bug Trends</h3>
              <p>Bug creation and resolution trends over time</p>
            </div>
            <div class="chart-container">
              <canvas #trendsChart></canvas>
            </div>
          </div>

          <!-- Project Distribution -->
          <div class="chart-card">
            <div class="chart-header">
              <h3>Bugs by Project</h3>
              <p>Bug distribution across projects</p>
            </div>
            <div class="chart-container">
              <canvas #projectChart></canvas>
            </div>
          </div>

          <!-- Assignee Distribution -->
          <div class="chart-card">
            <div class="chart-header">
              <h3>Bugs by Assignee</h3>
              <p>Bug assignment distribution</p>
            </div>
            <div class="chart-container">
              <canvas #assigneeChart></canvas>
            </div>
          </div>
        </div>
      </div>

      <!-- Error State -->
      <div *ngIf="errorMessage" class="error-container">
        <div class="error-card">
          <h3>Error Loading Report</h3>
          <p>{{ errorMessage }}</p>
          <button (click)="loadReportData()" class="btn btn-primary">Retry</button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .reports-container {
      padding: 2rem;
      max-width: 1400px;
      margin: 0 auto;
    }

    .reports-header {
      margin-bottom: 2rem;
    }

    .page-title {
      font-size: 2rem;
      font-weight: 700;
      color: var(--color-text-primary);
      margin-bottom: 0.5rem;
    }

    .page-subtitle {
      color: var(--color-text-secondary);
      font-size: 1.1rem;
    }

    .filters-section {
      background: white;
      border-radius: 12px;
      padding: 1.5rem;
      margin-bottom: 2rem;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .filters-form {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      align-items: end;
    }

    .filter-group {
      display: flex;
      flex-direction: column;
    }

    .filter-label {
      font-weight: 600;
      color: var(--color-text-primary);
      margin-bottom: 0.5rem;
      font-size: 0.9rem;
    }

    .date-range {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .date-separator {
      color: var(--color-text-secondary);
      font-size: 0.9rem;
    }

    .filter-actions {
      display: flex;
      gap: 0.5rem;
      flex-wrap: wrap;
    }

    .summary-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
      gap: 1.5rem;
      margin-bottom: 2rem;
    }

    .summary-card {
      background: white;
      border-radius: 12px;
      padding: 1.5rem;
      display: flex;
      align-items: center;
      gap: 1rem;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: transform 0.2s ease;
    }

    .summary-card:hover {
      transform: translateY(-2px);
    }

    .card-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
    }

    .card-icon.total { background: var(--color-primary); }
    .card-icon.open { background: var(--color-warning); }
    .card-icon.closed { background: var(--color-success); }
    .card-icon.critical { background: var(--color-danger); }
    .card-icon.resolution { background: var(--color-info); }

    .card-content h3 {
      font-size: 2rem;
      font-weight: 700;
      color: var(--color-text-primary);
      margin: 0;
    }

    .card-content p {
      color: var(--color-text-secondary);
      margin: 0;
      font-size: 0.9rem;
    }

    .charts-section {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 2rem;
    }

    .chart-card {
      background: white;
      border-radius: 12px;
      padding: 1.5rem;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .chart-card.full-width {
      grid-column: 1 / -1;
    }

    .chart-header {
      margin-bottom: 1.5rem;
    }

    .chart-header h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--color-text-primary);
      margin-bottom: 0.25rem;
    }

    .chart-header p {
      color: var(--color-text-secondary);
      font-size: 0.9rem;
      margin: 0;
    }

    .chart-container {
      position: relative;
      height: 300px;
    }

    .chart-card.full-width .chart-container {
      height: 400px;
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 4rem;
      color: var(--color-text-secondary);
    }

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid var(--color-border);
      border-top: 4px solid var(--color-primary);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 1rem;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .error-container {
      display: flex;
      justify-content: center;
      padding: 2rem;
    }

    .error-card {
      background: white;
      border-radius: 12px;
      padding: 2rem;
      text-align: center;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      max-width: 400px;
    }

    .error-card h3 {
      color: var(--color-danger);
      margin-bottom: 1rem;
    }

    .error-card p {
      color: var(--color-text-secondary);
      margin-bottom: 1.5rem;
    }

    @media (max-width: 768px) {
      .reports-container {
        padding: 1rem;
      }

      .filters-form {
        grid-template-columns: 1fr;
      }

      .summary-cards {
        grid-template-columns: 1fr;
      }

      .charts-section {
        grid-template-columns: 1fr;
      }

      .filter-actions {
        grid-column: 1 / -1;
        justify-content: stretch;
      }

      .filter-actions .btn {
        flex: 1;
      }
    }
  `]
})
export class BugReportsComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('statusChart') statusChartRef!: ElementRef<HTMLCanvasElement>;
  @ViewChild('severityChart') severityChartRef!: ElementRef<HTMLCanvasElement>;
  @ViewChild('trendsChart') trendsChartRef!: ElementRef<HTMLCanvasElement>;
  @ViewChild('projectChart') projectChartRef!: ElementRef<HTMLCanvasElement>;
  @ViewChild('assigneeChart') assigneeChartRef!: ElementRef<HTMLCanvasElement>;

  private fb = inject(FormBuilder);
  private bugService = inject(BugService);
  private projectService = inject(ProjectService);
  private userService = inject(UserService);

  filtersForm: FormGroup;
  loading = false;
  errorMessage = '';
  reportData: BugReportData | null = null;
  projects: Project[] = [];
  users: User[] = [];

  // Chart instances
  private statusChart: Chart | null = null;
  private severityChart: Chart | null = null;
  private trendsChart: Chart | null = null;
  private projectChart: Chart | null = null;
  private assigneeChart: Chart | null = null;

  constructor() {
    this.filtersForm = this.fb.group({
      startDate: [''],
      endDate: [''],
      projectId: [''],
      assigneeId: [''],
      status: ['']
    });
  }

  ngOnInit() {
    this.initializeDateRange();
    this.loadProjects();
    this.loadUsers();
    this.loadReportData();
  }

  ngAfterViewInit() {
    // Charts will be initialized after data is loaded
  }

  private initializeDateRange() {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - 6); // Last 6 months

    this.filtersForm.patchValue({
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0]
    });
  }

  private loadProjects() {
    // ProjectService.getAllProjects() returns Project[] directly, not Observable
    this.projects = this.projectService.getAllProjects();
  }

  private loadUsers() {
    this.userService.getAllUsers().subscribe({
      next: (users) => {
        this.users = users;
      },
      error: (error) => {
        console.error('Error loading users:', error);
      }
    });
  }

  loadReportData() {
    this.loading = true;
    this.errorMessage = '';

    const filters = this.filtersForm.value;

    this.bugService.getBugMetrics().subscribe({
      next: (metrics) => {
        this.reportData = this.transformMetricsToReportData(metrics);
        this.loading = false;

        // Initialize charts after data is loaded
        setTimeout(() => {
          this.initializeCharts();
        }, 100);
      },
      error: (error) => {
        this.loading = false;
        this.errorMessage = 'Failed to load report data. Please try again.';
        console.error('Error loading report data:', error);
      }
    });
  }

  private transformMetricsToReportData(metrics: any): BugReportData {
    // Transform the metrics data into the format needed for reports
    return {
      totalBugs: metrics.totalBugs || 0,
      openBugs: metrics.openBugs || 0,
      closedBugs: metrics.closedBugs || 0,
      criticalBugs: metrics.criticalBugs || 0,
      highPriorityBugs: metrics.highPriorityBugs || 0,
      averageResolutionTime: Math.round(metrics.averageResolutionTime || 0),
      statusDistribution: metrics.statusDistribution || {},
      severityDistribution: metrics.severityDistribution || {},
      priorityDistribution: metrics.priorityDistribution || {},
      monthlyTrends: this.generateMonthlyTrends(),
      projectDistribution: this.generateProjectDistribution(),
      assigneeDistribution: this.generateAssigneeDistribution()
    };
  }

  private generateMonthlyTrends() {
    // Generate mock monthly trends data
    const months = [];
    const currentDate = new Date();

    for (let i = 5; i >= 0; i--) {
      const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
      const monthName = date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });

      months.push({
        month: monthName,
        created: Math.floor(Math.random() * 50) + 10,
        resolved: Math.floor(Math.random() * 40) + 5
      });
    }

    return months;
  }

  private generateProjectDistribution() {
    // Generate project distribution based on available projects
    return this.projects.slice(0, 5).map(project => ({
      projectName: project.name,
      bugCount: Math.floor(Math.random() * 30) + 5
    }));
  }

  private generateAssigneeDistribution() {
    // Generate assignee distribution based on available users
    return this.users.slice(0, 6).map(user => ({
      assigneeName: `${user.firstName} ${user.lastName}`,
      bugCount: Math.floor(Math.random() * 20) + 2
    }));
  }

  applyFilters() {
    this.loadReportData();
  }

  resetFilters() {
    this.filtersForm.reset();
    this.initializeDateRange();
    this.loadReportData();
  }

  exportReport() {
    // TODO: Implement export functionality
    console.log('Export report functionality to be implemented');
    alert('Export functionality will be implemented in the next phase');
  }

  private initializeCharts() {
    if (!this.reportData) return;

    this.destroyCharts();

    this.createStatusChart();
    this.createSeverityChart();
    this.createTrendsChart();
    this.createProjectChart();
    this.createAssigneeChart();
  }

  private destroyCharts() {
    [this.statusChart, this.severityChart, this.trendsChart, this.projectChart, this.assigneeChart]
      .forEach(chart => chart?.destroy());
  }

  private createStatusChart() {
    if (!this.statusChartRef?.nativeElement || !this.reportData) return;

    const ctx = this.statusChartRef.nativeElement.getContext('2d');
    if (!ctx) return;

    const data = this.reportData.statusDistribution;
    const labels = Object.keys(data);
    const values = Object.values(data);

    this.statusChart = new Chart(ctx, {
      type: 'doughnut',
      data: {
        labels: labels,
        datasets: [{
          data: values,
          backgroundColor: [
            '#3B82F6', // Blue
            '#F59E0B', // Yellow
            '#10B981', // Green
            '#EF4444', // Red
            '#8B5CF6'  // Purple
          ],
          borderWidth: 2,
          borderColor: '#ffffff'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom',
            labels: {
              padding: 20,
              usePointStyle: true
            }
          }
        }
      }
    });
  }

  private createSeverityChart() {
    if (!this.severityChartRef?.nativeElement || !this.reportData) return;

    const ctx = this.severityChartRef.nativeElement.getContext('2d');
    if (!ctx) return;

    const data = this.reportData.severityDistribution;
    const labels = Object.keys(data);
    const values = Object.values(data);

    this.severityChart = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: labels,
        datasets: [{
          label: 'Bug Count',
          data: values,
          backgroundColor: [
            '#EF4444', // Critical - Red
            '#F59E0B', // High - Orange
            '#3B82F6', // Medium - Blue
            '#10B981'  // Low - Green
          ],
          borderRadius: 8,
          borderSkipped: false
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            grid: {
              color: '#f3f4f6'
            }
          },
          x: {
            grid: {
              display: false
            }
          }
        }
      }
    });
  }

  private createTrendsChart() {
    if (!this.trendsChartRef?.nativeElement || !this.reportData) return;

    const ctx = this.trendsChartRef.nativeElement.getContext('2d');
    if (!ctx) return;

    const trends = this.reportData.monthlyTrends;
    const labels = trends.map(t => t.month);
    const createdData = trends.map(t => t.created);
    const resolvedData = trends.map(t => t.resolved);

    this.trendsChart = new Chart(ctx, {
      type: 'line',
      data: {
        labels: labels,
        datasets: [
          {
            label: 'Bugs Created',
            data: createdData,
            borderColor: '#EF4444',
            backgroundColor: 'rgba(239, 68, 68, 0.1)',
            tension: 0.4,
            fill: true
          },
          {
            label: 'Bugs Resolved',
            data: resolvedData,
            borderColor: '#10B981',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            tension: 0.4,
            fill: true
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'top'
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            grid: {
              color: '#f3f4f6'
            }
          },
          x: {
            grid: {
              display: false
            }
          }
        }
      }
    });
  }

  private createProjectChart() {
    if (!this.projectChartRef?.nativeElement || !this.reportData) return;

    const ctx = this.projectChartRef.nativeElement.getContext('2d');
    if (!ctx) return;

    const projects = this.reportData.projectDistribution;
    const labels = projects.map(p => p.projectName);
    const values = projects.map(p => p.bugCount);

    this.projectChart = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: labels,
        datasets: [{
          label: 'Bug Count',
          data: values,
          backgroundColor: '#3B82F6',
          borderRadius: 8,
          borderSkipped: false
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        indexAxis: 'y',
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          x: {
            beginAtZero: true,
            grid: {
              color: '#f3f4f6'
            }
          },
          y: {
            grid: {
              display: false
            }
          }
        }
      }
    });
  }

  private createAssigneeChart() {
    if (!this.assigneeChartRef?.nativeElement || !this.reportData) return;

    const ctx = this.assigneeChartRef.nativeElement.getContext('2d');
    if (!ctx) return;

    const assignees = this.reportData.assigneeDistribution;
    const labels = assignees.map(a => a.assigneeName);
    const values = assignees.map(a => a.bugCount);

    this.assigneeChart = new Chart(ctx, {
      type: 'doughnut',
      data: {
        labels: labels,
        datasets: [{
          data: values,
          backgroundColor: [
            '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4'
          ],
          borderWidth: 2,
          borderColor: '#ffffff'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom',
            labels: {
              padding: 20,
              usePointStyle: true
            }
          }
        }
      }
    });
  }

  ngOnDestroy() {
    this.destroyCharts();
  }
}
