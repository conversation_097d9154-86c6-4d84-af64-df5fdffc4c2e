import { User, UserRole } from './user.model';

export interface Project {
  id: string;
  name: string;
  description: string;
  projectUrl: string;
  modules: ProjectModule[];
  teamMembers: ProjectTeamMember[];
  status: ProjectStatus;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
}

export interface ProjectModule {
  id: string;
  name: string;
  description?: string;
  features: ProjectFeature[];
  projectId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ProjectFeature {
  id: string;
  name: string;
  description?: string;
  moduleId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ProjectTeamMember {
  id: string;
  userId: string;
  user: User;
  projectId: string;
  role: UserRole;
  assignedAt: Date;
  assignedBy: string;
}

export enum ProjectStatus {
  PLANNING = 'Planning',
  ACTIVE = 'Active',
  ON_HOLD = 'On Hold',
  COMPLETED = 'Completed',
  ARCHIVED = 'Archived'
}

export interface CreateProjectRequest {
  name: string;
  description: string;
  projectUrl: string;
  modules: CreateModuleRequest[];
  teamMembers: CreateTeamMemberRequest[];
}

export interface CreateModuleRequest {
  name: string;
  description?: string;
  features: CreateFeatureRequest[];
}

export interface CreateFeatureRequest {
  name: string;
  description?: string;
}

export interface CreateTeamMemberRequest {
  userId: string;
  role: UserRole;
}

export interface UpdateProjectRequest {
  name?: string;
  description?: string;
  projectUrl?: string;
  status?: ProjectStatus;
  isActive?: boolean;
}

export interface UpdateModuleRequest {
  name?: string;
  description?: string;
}

export interface UpdateFeatureRequest {
  name?: string;
  description?: string;
}

export interface ProjectSummary {
  id: string;
  name: string;
  description: string;
  status: ProjectStatus;
  totalModules: number;
  totalFeatures: number;
  totalTeamMembers: number;
  totalBugs: number;
  activeBugs: number;
  resolvedBugs: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface ProjectMetrics {
  projectId: string;
  totalBugsRaised: number;
  totalBugsAssigned: number;
  totalBugsFixed: number;
  totalBugsPending: number;
  fixRate: number; // (Fixed / Assigned) * 100
  reopenRate: number; // (Reopened / Fixed) * 100
  averageTimeToFix: number; // in hours
  averageTimeToRetest: number; // in hours
  bugsByPriority: BugPriorityDistribution;
  bugsBySeverity: BugSeverityDistribution;
  bugsOverTime: BugTrendData[];
}

export interface BugPriorityDistribution {
  low: number;
  medium: number;
  high: number;
  critical: number;
}

export interface BugSeverityDistribution {
  low: number;
  medium: number;
  high: number;
  critical: number;
}

export interface BugTrendData {
  date: Date;
  opened: number;
  closed: number;
  total: number;
}

export interface ProjectFilter {
  status?: ProjectStatus[];
  teamMemberRole?: UserRole[];
  createdDateRange?: {
    start: Date;
    end: Date;
  };
  search?: string;
  isActive?: boolean;
}

export interface ProjectListResponse {
  projects: ProjectSummary[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// Helper functions
export function getProjectStatusColor(status: ProjectStatus): string {
  const statusColors: Record<ProjectStatus, string> = {
    [ProjectStatus.PLANNING]: 'warning',
    [ProjectStatus.ACTIVE]: 'success',
    [ProjectStatus.ON_HOLD]: 'error',
    [ProjectStatus.COMPLETED]: 'info',
    [ProjectStatus.ARCHIVED]: 'gray'
  };
  return statusColors[status];
}

export function calculateProjectProgress(project: Project): number {
  // This would typically be based on completed features or milestones
  // For now, return a mock calculation
  const totalFeatures = project.modules.reduce((sum, module) => sum + module.features.length, 0);
  if (totalFeatures === 0) return 0;
  
  // Mock: assume 70% completion for active projects
  switch (project.status) {
    case ProjectStatus.COMPLETED:
      return 100;
    case ProjectStatus.ACTIVE:
      return 70;
    case ProjectStatus.PLANNING:
      return 10;
    case ProjectStatus.ON_HOLD:
      return 45;
    case ProjectStatus.ARCHIVED:
      return 100;
    default:
      return 0;
  }
}

export function getTeamMembersByRole(project: Project, role: UserRole): ProjectTeamMember[] {
  return project.teamMembers.filter(member => member.role === role);
}

export function isUserProjectMember(project: Project, userId: string): boolean {
  return project.teamMembers.some(member => member.userId === userId);
}

export function canUserEditProject(project: Project, userId: string, userRole: UserRole): boolean {
  if (userRole === UserRole.ADMIN) return true;
  if (project.createdBy === userId) return true;
  
  const member = project.teamMembers.find(m => m.userId === userId);
  return member?.role === UserRole.PROJECT_MANAGER;
}
