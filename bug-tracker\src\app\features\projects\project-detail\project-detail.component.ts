import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { ProjectService } from '../../../core/services/project.service';
import { Project, ProjectStatus, getProjectStatusColor } from '../../../core/models/project.model';
import { UserRole } from '../../../core/models/user.model';

@Component({
  selector: 'app-project-detail',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="project-detail" *ngIf="project; else loading">
      <!-- Header -->
      <div class="project-detail__header">
        <div class="header-content">
          <div class="breadcrumb">
            <a routerLink="/projects" class="breadcrumb-link">Projects</a>
            <span class="breadcrumb-separator">/</span>
            <span class="breadcrumb-current">{{ project.name }}</span>
          </div>
          <div class="project-title">
            <h1>{{ project.name }}</h1>
            <span class="project-status" [class]="'project-status--' + getStatusClass(project.status)">
              {{ project.status }}
            </span>
          </div>
          <p class="project-description" *ngIf="project.description">{{ project.description }}</p>
        </div>
        <div class="header-actions">
          <a [routerLink]="['/projects', project.id, 'edit']" class="btn btn-primary">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
            </svg>
            Edit Project
          </a>
          <a routerLink="/projects" class="btn btn-outline">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
            </svg>
            Back to Projects
          </a>
        </div>
      </div>

      <!-- Project Stats -->
      <div class="project-stats">
        <div class="stat-card">
          <div class="stat-icon stat-icon--modules">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="9" y1="9" x2="15" y2="15"></line>
              <line x1="15" y1="9" x2="9" y2="15"></line>
            </svg>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ project.modules.length }}</div>
            <div class="stat-label">Modules</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon stat-icon--features">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ getTotalFeatures() }}</div>
            <div class="stat-label">Features</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon stat-icon--team">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            </svg>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ project.teamMembers.length }}</div>
            <div class="stat-label">Team Members</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon stat-icon--progress">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <circle cx="12" cy="12" r="10"></circle>
              <path d="M12 6v6l4 2"></path>
            </svg>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ getProjectProgress(project.status) }}%</div>
            <div class="stat-label">Progress</div>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="project-content">
        <!-- Project Information -->
        <div class="content-section">
          <div class="section-card">
            <div class="section-header">
              <h2>Project Information</h2>
            </div>
            <div class="section-content">
              <div class="info-grid">
                <div class="info-item">
                  <label>Project URL</label>
                  <div class="info-value">
                    <a [href]="project.projectUrl" target="_blank" class="project-url">
                      {{ project.projectUrl }}
                      <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M18 13v6a2 2 0 01-2 2H5a2 2 0 01-2-2V8a2 2 0 012-2h6m4-3h6v6m-11 5L21 3"/>
                      </svg>
                    </a>
                  </div>
                </div>
                <div class="info-item">
                  <label>Created</label>
                  <div class="info-value">{{ formatDate(project.createdAt) }}</div>
                </div>
                <div class="info-item">
                  <label>Last Updated</label>
                  <div class="info-value">{{ formatDate(project.updatedAt) }}</div>
                </div>
                <div class="info-item">
                  <label>Status</label>
                  <div class="info-value">
                    <span class="project-status" [class]="'project-status--' + getStatusClass(project.status)">
                      {{ project.status }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Modules and Features -->
        <div class="content-section">
          <div class="section-card">
            <div class="section-header">
              <h2>Modules & Features</h2>
              <span class="section-count">{{ project.modules.length }} modules</span>
            </div>
            <div class="section-content">
              <div class="modules-grid" *ngIf="project.modules.length > 0; else noModules">
                <div class="module-card" *ngFor="let module of project.modules">
                  <div class="module-header">
                    <h3>{{ module.name }}</h3>
                    <span class="feature-count">{{ module.features.length }} features</span>
                  </div>
                  <p class="module-description" *ngIf="module.description">{{ module.description }}</p>

                  <div class="features-list" *ngIf="module.features.length > 0">
                    <h4>Features</h4>
                    <div class="feature-item" *ngFor="let feature of module.features">
                      <div class="feature-info">
                        <span class="feature-name">{{ feature.name }}</span>
                        <p class="feature-description" *ngIf="feature.description">{{ feature.description }}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <ng-template #noModules>
                <div class="empty-state">
                  <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1">
                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                    <line x1="9" y1="9" x2="15" y2="15"></line>
                    <line x1="15" y1="9" x2="9" y2="15"></line>
                  </svg>
                  <h3>No Modules</h3>
                  <p>This project doesn't have any modules yet.</p>
                </div>
              </ng-template>
            </div>
          </div>
        </div>

        <!-- Team Members -->
        <div class="content-section">
          <div class="section-card">
            <div class="section-header">
              <h2>Team Members</h2>
              <span class="section-count">{{ project.teamMembers.length }} members</span>
            </div>
            <div class="section-content">
              <div class="team-by-role" *ngFor="let roleGroup of teamMembersByRole">
                <div class="role-section">
                  <h4>{{ roleGroup.role }} ({{ roleGroup.members.length }})</h4>
                  <div class="team-members-grid">
                    <div class="team-member-card" *ngFor="let member of roleGroup.members">
                      <div class="member-avatar">{{ getInitials(member.user.fullName) }}</div>
                      <div class="member-info">
                        <div class="member-name">{{ member.user.fullName }}</div>
                        <div class="member-email">{{ member.user.email }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="empty-state" *ngIf="project.teamMembers.length === 0">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1">
                  <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                  <circle cx="9" cy="7" r="4"></circle>
                  <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                  <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                </svg>
                <h3>No Team Members</h3>
                <p>No team members have been assigned to this project yet.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <ng-template #loading>
      <div class="loading-state">
        <div class="loading-spinner"></div>
        <p>Loading project details...</p>
      </div>
    </ng-template>

    <!-- Error State -->
    <div class="error-state" *ngIf="error">
      <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1">
        <circle cx="12" cy="12" r="10"></circle>
        <line x1="15" y1="9" x2="9" y2="15"></line>
        <line x1="9" y1="9" x2="15" y2="15"></line>
      </svg>
      <h2>Error Loading Project</h2>
      <p>{{ error }}</p>
      <button class="btn btn-primary" (click)="loadProject()">Try Again</button>
    </div>
  `,
  styles: [`
    .project-detail {
      padding: var(--spacing-6);
      max-width: 1200px;
      margin: 0 auto;
    }

    /* Header */
    .project-detail__header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: var(--spacing-8);
      gap: var(--spacing-6);
    }

    .header-content {
      flex: 1;
    }

    .breadcrumb {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      margin-bottom: var(--spacing-3);
      font-size: var(--font-size-sm);
    }

    .breadcrumb-link {
      color: var(--color-primary-600);
      text-decoration: none;
      transition: color var(--transition-fast);
    }

    .breadcrumb-link:hover {
      color: var(--color-primary-700);
    }

    .breadcrumb-separator {
      color: var(--color-gray-400);
    }

    .breadcrumb-current {
      color: var(--color-gray-600);
    }

    .project-title {
      display: flex;
      align-items: center;
      gap: var(--spacing-3);
      margin-bottom: var(--spacing-2);
    }

    .project-title h1 {
      font-size: var(--font-size-3xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-gray-900);
      margin: 0;
    }

    .project-description {
      color: var(--color-gray-600);
      font-size: var(--font-size-lg);
      line-height: 1.6;
      margin: 0;
    }

    .header-actions {
      display: flex;
      gap: var(--spacing-3);
      flex-shrink: 0;
    }

    /* Project Status */
    .project-status {
      padding: var(--spacing-1) var(--spacing-3);
      border-radius: var(--border-radius-full);
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }

    .project-status--planning {
      background: var(--color-blue-100);
      color: var(--color-blue-800);
    }

    .project-status--active {
      background: var(--color-green-100);
      color: var(--color-green-800);
    }

    .project-status--on-hold {
      background: var(--color-yellow-100);
      color: var(--color-yellow-800);
    }

    .project-status--completed {
      background: var(--color-purple-100);
      color: var(--color-purple-800);
    }

    .project-status--archived {
      background: var(--color-gray-100);
      color: var(--color-gray-800);
    }

    /* Stats */
    .project-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--spacing-4);
      margin-bottom: var(--spacing-8);
    }

    .stat-card {
      background: var(--color-white);
      border: 1px solid var(--color-gray-200);
      border-radius: var(--border-radius-lg);
      padding: var(--spacing-6);
      display: flex;
      align-items: center;
      gap: var(--spacing-4);
      transition: all var(--transition-fast);
    }

    .stat-card:hover {
      border-color: var(--color-gray-300);
      box-shadow: var(--shadow-md);
    }

    .stat-icon {
      width: 48px;
      height: 48px;
      border-radius: var(--border-radius-lg);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
    }

    .stat-icon--modules {
      background: var(--color-blue-100);
      color: var(--color-blue-600);
    }

    .stat-icon--features {
      background: var(--color-green-100);
      color: var(--color-green-600);
    }

    .stat-icon--team {
      background: var(--color-purple-100);
      color: var(--color-purple-600);
    }

    .stat-icon--progress {
      background: var(--color-orange-100);
      color: var(--color-orange-600);
    }

    .stat-content {
      flex: 1;
    }

    .stat-value {
      font-size: var(--font-size-2xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-gray-900);
      line-height: 1;
    }

    .stat-label {
      font-size: var(--font-size-sm);
      color: var(--color-gray-600);
      margin-top: var(--spacing-1);
    }

    /* Content Sections */
    .project-content {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-8);
    }

    .content-section {
      width: 100%;
    }

    .section-card {
      background: var(--color-white);
      border: 1px solid var(--color-gray-200);
      border-radius: var(--border-radius-lg);
      overflow: hidden;
    }

    .section-header {
      padding: var(--spacing-6);
      border-bottom: 1px solid var(--color-gray-200);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .section-header h2 {
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-900);
      margin: 0;
    }

    .section-count {
      font-size: var(--font-size-sm);
      color: var(--color-gray-600);
      background: var(--color-gray-100);
      padding: var(--spacing-1) var(--spacing-3);
      border-radius: var(--border-radius-full);
    }

    .section-content {
      padding: var(--spacing-6);
    }

    /* Project Information */
    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: var(--spacing-6);
    }

    .info-item {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-2);
    }

    .info-item label {
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-700);
    }

    .info-value {
      font-size: var(--font-size-base);
      color: var(--color-gray-900);
    }

    .project-url {
      color: var(--color-primary-600);
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: var(--spacing-1);
      transition: color var(--transition-fast);
    }

    .project-url:hover {
      color: var(--color-primary-700);
    }

    /* Modules */
    .modules-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: var(--spacing-6);
    }

    .module-card {
      background: var(--color-gray-50);
      border: 1px solid var(--color-gray-200);
      border-radius: var(--border-radius-lg);
      padding: var(--spacing-6);
      transition: all var(--transition-fast);
    }

    .module-card:hover {
      border-color: var(--color-gray-300);
      box-shadow: var(--shadow-sm);
    }

    .module-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-4);
    }

    .module-header h3 {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-900);
      margin: 0;
    }

    .feature-count {
      font-size: var(--font-size-sm);
      color: var(--color-gray-600);
      background: var(--color-white);
      padding: var(--spacing-1) var(--spacing-2);
      border-radius: var(--border-radius-md);
    }

    .module-description {
      color: var(--color-gray-600);
      font-size: var(--font-size-sm);
      line-height: 1.5;
      margin: 0 0 var(--spacing-4) 0;
    }

    .features-list h4 {
      font-size: var(--font-size-base);
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-800);
      margin: 0 0 var(--spacing-3) 0;
    }

    .feature-item {
      background: var(--color-white);
      border: 1px solid var(--color-gray-200);
      border-radius: var(--border-radius-md);
      padding: var(--spacing-3);
      margin-bottom: var(--spacing-2);
    }

    .feature-item:last-child {
      margin-bottom: 0;
    }

    .feature-name {
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-900);
    }

    .feature-description {
      font-size: var(--font-size-xs);
      color: var(--color-gray-600);
      margin: var(--spacing-1) 0 0 0;
      line-height: 1.4;
    }

    /* Team Members */
    .team-by-role {
      margin-bottom: var(--spacing-6);
    }

    .team-by-role:last-child {
      margin-bottom: 0;
    }

    .role-section h4 {
      font-size: var(--font-size-base);
      font-weight: var(--font-weight-semibold);
      color: var(--color-primary-700);
      margin: 0 0 var(--spacing-4) 0;
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }

    .team-members-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: var(--spacing-4);
    }

    .team-member-card {
      background: var(--color-gray-50);
      border: 1px solid var(--color-gray-200);
      border-radius: var(--border-radius-lg);
      padding: var(--spacing-4);
      display: flex;
      align-items: center;
      gap: var(--spacing-3);
      transition: all var(--transition-fast);
    }

    .team-member-card:hover {
      border-color: var(--color-gray-300);
      background: var(--color-white);
    }

    .member-avatar {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      background: var(--color-primary-100);
      color: var(--color-primary-700);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-semibold);
      flex-shrink: 0;
    }

    .member-info {
      flex: 1;
    }

    .member-name {
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-900);
      margin-bottom: var(--spacing-1);
    }

    .member-email {
      font-size: var(--font-size-xs);
      color: var(--color-gray-600);
    }

    /* Empty States */
    .empty-state {
      text-align: center;
      padding: var(--spacing-12) var(--spacing-6);
      color: var(--color-gray-500);
    }

    .empty-state svg {
      margin-bottom: var(--spacing-4);
      opacity: 0.6;
    }

    .empty-state h3 {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-700);
      margin: 0 0 var(--spacing-2) 0;
    }

    .empty-state p {
      font-size: var(--font-size-sm);
      margin: 0;
    }

    /* Loading State */
    .loading-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: var(--spacing-16);
      color: var(--color-gray-600);
    }

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid var(--color-gray-200);
      border-top: 3px solid var(--color-primary-600);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: var(--spacing-4);
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Error State */
    .error-state {
      text-align: center;
      padding: var(--spacing-16);
      color: var(--color-gray-600);
    }

    .error-state svg {
      margin-bottom: var(--spacing-4);
      color: var(--color-error-500);
    }

    .error-state h2 {
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-900);
      margin: 0 0 var(--spacing-2) 0;
    }

    .error-state p {
      font-size: var(--font-size-sm);
      margin: 0 0 var(--spacing-4) 0;
    }

    /* Buttons */
    .btn {
      padding: var(--spacing-2) var(--spacing-4);
      border-radius: var(--border-radius-md);
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      cursor: pointer;
      transition: all var(--transition-fast);
      border: 1px solid transparent;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: var(--spacing-2);
      text-decoration: none;
    }

    .btn-primary {
      background: var(--color-primary-600);
      color: var(--color-white);
      border-color: var(--color-primary-600);
    }

    .btn-primary:hover {
      background: var(--color-primary-700);
      border-color: var(--color-primary-700);
    }

    .btn-outline {
      background: var(--color-white);
      color: var(--color-gray-700);
      border-color: var(--color-gray-300);
    }

    .btn-outline:hover {
      background: var(--color-gray-50);
      border-color: var(--color-gray-400);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .project-detail {
        padding: var(--spacing-4);
      }

      .project-detail__header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-4);
      }

      .header-actions {
        justify-content: flex-start;
      }

      .project-stats {
        grid-template-columns: repeat(2, 1fr);
      }

      .modules-grid {
        grid-template-columns: 1fr;
      }

      .team-members-grid {
        grid-template-columns: 1fr;
      }

      .info-grid {
        grid-template-columns: 1fr;
      }
    }

    @media (max-width: 480px) {
      .project-stats {
        grid-template-columns: 1fr;
      }

      .stat-card {
        padding: var(--spacing-4);
      }

      .project-title {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-2);
      }
    }
  `]
})
export class ProjectDetailComponent implements OnInit {
  private projectService = inject(ProjectService);
  private route = inject(ActivatedRoute);
  private router = inject(Router);

  project: Project | null = null;
  loading = false;
  error: string | null = null;

  ngOnInit() {
    this.loadProject();
  }

  get teamMembersByRole() {
    if (!this.project) return [];

    const roleGroups: { role: UserRole; members: any[] }[] = [];
    const roles = Object.values(UserRole);

    roles.forEach(role => {
      const members = this.project!.teamMembers.filter(member => member.role === role);
      if (members.length > 0) {
        roleGroups.push({ role, members });
      }
    });

    return roleGroups;
  }

  loadProject() {
    const projectId = this.route.snapshot.paramMap.get('id');
    if (!projectId) {
      this.error = 'Project ID not found';
      return;
    }

    this.loading = true;
    this.error = null;

    this.projectService.getProjectById(projectId).subscribe({
      next: (project: Project | null) => {
        if (project) {
          this.project = project;
        } else {
          this.error = 'Project not found';
        }
        this.loading = false;
      },
      error: (error: any) => {
        this.error = 'Failed to load project details';
        this.loading = false;
        console.error('Error loading project:', error);
      }
    });
  }

  getStatusClass(status: ProjectStatus): string {
    return getProjectStatusColor(status);
  }

  getProjectProgress(status: ProjectStatus): number {
    switch (status) {
      case 'Planning': return 10;
      case 'Active': return 50;
      case 'On Hold': return 30;
      case 'Completed': return 100;
      case 'Archived': return 100;
      default: return 0;
    }
  }

  formatDate(date: Date): string {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(new Date(date));
  }

  getInitials(fullName: string): string {
    return fullName.split(' ').map(name => name[0]).join('').toUpperCase().slice(0, 2);
  }

  getTotalFeatures(): number {
    if (!this.project) return 0;
    return this.project.modules.reduce((total, module) => total + module.features.length, 0);
  }
}
