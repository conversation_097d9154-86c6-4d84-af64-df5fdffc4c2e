import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet } from '@angular/router';
import { AuthService } from './core/services/auth.service';
import { UserService } from './core/services/user.service';

@Component({
  selector: 'app-root',
  imports: [CommonModule, RouterOutlet],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss'
})
export class AppComponent implements OnInit {
  title = 'BugTracker Pro';

  private authService = inject(AuthService);
  private userService = inject(UserService);

  ngOnInit() {
    // Initialize services and check authentication state
    this.initializeApp();
  }

  private initializeApp() {
    // The services will automatically initialize from localStorage
    // This is where you might also check token validity, refresh tokens, etc.
  }
}
