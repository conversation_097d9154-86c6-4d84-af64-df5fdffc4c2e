import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute, RouterLink } from '@angular/router';
import { BugCommentsComponent } from '../bug-comments/bug-comments.component';
import { BugWatchersComponent } from '../bug-watchers/bug-watchers.component';
import { BugService } from '../../../core/services/bug.service';
import { UserService } from '../../../core/services/user.service';
import { AuthService } from '../../../core/services/auth.service';
import {
  Bug,
  BugStatus,
  BugSeverity,
  BugPriority,
  BugAuditLog,
  getBugStatusColor,
  getBugSeverityColor,
  getBugPriorityColor,
  getNextValidStatuses
} from '../../../core/models/bug.model';
import { User, UserRole } from '../../../core/models/user.model';
import { AuthUser } from '../../../core/models/user.model';

@Component({
  selector: 'app-bug-detail',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, RouterLink, BugCommentsComponent, BugWatchersComponent],
  template: `
    <div class="bug-detail">
      <!-- Loading State -->
      <div *ngIf="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <p>Loading bug details...</p>
      </div>

      <!-- Error State -->
      <div *ngIf="errorMessage && !loading" class="error-container">
        <div class="error-banner">
          <svg class="error-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="10"/>
            <line x1="15" y1="9" x2="9" y2="15"/>
            <line x1="9" y1="9" x2="15" y2="15"/>
          </svg>
          <div>
            <h3>Error Loading Bug</h3>
            <p>{{ errorMessage }}</p>
          </div>
        </div>
        <div class="error-actions">
          <button class="btn btn-primary" (click)="loadBug()">Try Again</button>
          <button class="btn btn-secondary" (click)="goBack()">Go Back</button>
        </div>
      </div>

      <!-- Bug Detail Content -->
      <div *ngIf="bug && !loading" class="bug-detail__content">
        <!-- Header -->
        <div class="bug-detail__header">
          <div class="bug-detail__title-section">
            <div class="bug-detail__breadcrumb">
              <a [routerLink]="['/bugs']" class="breadcrumb-link">Bugs</a>
              <span class="breadcrumb-separator">/</span>
              <span class="breadcrumb-current">{{ bug.id }}</span>
            </div>
            <h1 class="bug-detail__title">{{ bug.title }}</h1>
            <div class="bug-detail__meta">
              <span class="meta-item">
                <strong>Reported by:</strong> {{ bug.reportedBy.fullName }}
              </span>
              <span class="meta-item">
                <strong>Created:</strong> {{ formatDate(bug.issueDate) }}
              </span>
              <span class="meta-item" *ngIf="bug.updatedAt !== bug.createdAt">
                <strong>Updated:</strong> {{ formatDate(bug.updatedAt) }}
              </span>
            </div>
          </div>
          <div class="bug-detail__actions">
            <button
              class="btn btn-secondary"
              [routerLink]="['/bugs', bug.id, 'edit']"
              *ngIf="canEditBug()"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
              </svg>
              Edit Bug
            </button>
          </div>
        </div>

        <!-- Status and Priority Badges -->
        <div class="bug-detail__badges">
          <span class="badge" [class]="'badge--' + getBugStatusColor(bug.status)">
            {{ bug.status }}
          </span>
          <span class="badge" [class]="'badge--' + getBugSeverityColor(bug.severity)">
            {{ bug.severity }} Severity
          </span>
          <span class="badge" [class]="'badge--' + getBugPriorityColor(bug.priority)">
            {{ bug.priority }} Priority
          </span>
          <span class="badge badge--gray" *ngIf="isOverdue()">
            Overdue
          </span>
        </div>

        <!-- Main Content Grid -->
        <div class="bug-detail__grid">
          <!-- Left Column - Bug Information -->
          <div class="bug-detail__main">
            <!-- Description Section -->
            <div class="detail-section">
              <h2 class="section-title">Description</h2>
              <div class="section-content">
                <p class="description-text">{{ bug.description }}</p>
              </div>
            </div>

            <!-- Steps to Reproduce Section -->
            <div class="detail-section">
              <h2 class="section-title">Steps to Reproduce</h2>
              <div class="section-content">
                <pre class="steps-text">{{ bug.stepsToReproduce }}</pre>
              </div>
            </div>

            <!-- Tags Section -->
            <div class="detail-section" *ngIf="bug.tags && bug.tags.length > 0">
              <h2 class="section-title">Tags</h2>
              <div class="section-content">
                <div class="tags-container">
                  <span class="tag" *ngFor="let tag of bug.tags">{{ tag }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Right Column - Sidebar -->
          <div class="bug-detail__sidebar">
            <!-- Status Management -->
            <div class="sidebar-section" *ngIf="canChangeStatus()">
              <h3 class="sidebar-title">Status Management</h3>
              <div class="status-actions">
                <select
                  class="form-select"
                  [value]="bug.status"
                  (change)="onStatusChange($event)"
                  [disabled]="updatingStatus"
                >
                  <option [value]="bug.status">{{ bug.status }}</option>
                  <option
                    *ngFor="let status of getAvailableStatuses()"
                    [value]="status"
                  >
                    {{ status }}
                  </option>
                </select>
                <div class="status-help" *ngIf="getAvailableStatuses().length === 0">
                  No status changes available for your role.
                </div>
              </div>
            </div>

            <!-- Project Information -->
            <div class="sidebar-section">
              <h3 class="sidebar-title">Project Information</h3>
              <div class="info-grid">
                <div class="info-item">
                  <label>Project:</label>
                  <span>{{ bug.project.name }}</span>
                </div>
                <div class="info-item">
                  <label>Module:</label>
                  <span>{{ bug.module.name }}</span>
                </div>
                <div class="info-item">
                  <label>Feature:</label>
                  <span>{{ bug.feature.name }}</span>
                </div>
              </div>
            </div>

            <!-- Assignment Information -->
            <div class="sidebar-section">
              <h3 class="sidebar-title">Assignment</h3>
              <div class="assignment-grid">
                <div class="assignment-item">
                  <label>QA Assigned:</label>
                  <div class="assignee">
                    <span *ngIf="bug.qaAssigned; else unassignedQA">
                      {{ bug.qaAssigned.fullName }}
                    </span>
                    <ng-template #unassignedQA>
                      <span class="unassigned">Unassigned</span>
                    </ng-template>
                  </div>
                </div>
                <div class="assignment-item">
                  <label>Developer Assigned:</label>
                  <div class="assignee">
                    <span *ngIf="bug.devAssigned; else unassignedDev">
                      {{ bug.devAssigned.fullName }}
                    </span>
                    <ng-template #unassignedDev>
                      <span class="unassigned">Unassigned</span>
                    </ng-template>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Timeline Information -->
          <div class="sidebar-section">
            <h3 class="sidebar-title">Timeline</h3>
            <div class="timeline-grid">
              <div class="timeline-item" *ngIf="bug.expectedReviewDate">
                <label>Expected Review:</label>
                <span>{{ formatDate(bug.expectedReviewDate) }}</span>
              </div>
              <div class="timeline-item" *ngIf="bug.expectedFixDate">
                <label>Expected Fix:</label>
                <span [class.overdue]="isOverdue()">{{ formatDate(bug.expectedFixDate) }}</span>
              </div>
              <div class="timeline-item" *ngIf="bug.actualFixDate">
                <label>Actual Fix:</label>
                <span>{{ formatDate(bug.actualFixDate) }}</span>
              </div>
              <div class="timeline-item" *ngIf="bug.closedDate">
                <label>Closed:</label>
                <span>{{ formatDate(bug.closedDate) }}</span>
              </div>
            </div>
          </div>

          <!-- Effort Information -->
          <div class="sidebar-section" *ngIf="bug.estimatedHours || bug.actualHours">
            <h3 class="sidebar-title">Effort</h3>
            <div class="effort-grid">
              <div class="effort-item" *ngIf="bug.estimatedHours">
                <label>Estimated Hours:</label>
                <span>{{ bug.estimatedHours }}h</span>
              </div>
              <div class="effort-item" *ngIf="bug.actualHours">
                <label>Actual Hours:</label>
                <span>{{ bug.actualHours }}h</span>
              </div>
            </div>
          </div>

          <!-- Statistics -->
          <div class="sidebar-section">
            <h3 class="sidebar-title">Statistics</h3>
            <div class="stats-grid">
              <div class="stat-item">
                <label>Days Open:</label>
                <span>{{ getDaysOpen() }}</span>
              </div>
              <div class="stat-item" *ngIf="bug.reopenCount > 0">
                <label>Reopen Count:</label>
                <span>{{ bug.reopenCount }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Activity Timeline -->
        <div class="bug-detail__activity" *ngIf="bug.auditLogs && bug.auditLogs.length > 0">
          <h2 class="activity-title">Activity Timeline</h2>
          <div class="activity-timeline">
            <div
              class="activity-item"
              *ngFor="let log of bug.auditLogs; trackBy: trackByLogId"
            >
              <div class="activity-icon">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="12" cy="12" r="3"/>
                  <path d="M12 1v6m0 6v6"/>
                </svg>
              </div>
              <div class="activity-content">
                <div class="activity-header">
                  <span class="activity-user">{{ log.performedBy.fullName }}</span>
                  <span class="activity-action">{{ log.description }}</span>
                  <span class="activity-time">{{ formatDate(log.performedAt) }}</span>
                </div>
                <div class="activity-details" *ngIf="log.oldValue || log.newValue">
                  <span *ngIf="log.oldValue" class="old-value">{{ log.oldValue }}</span>
                  <span *ngIf="log.oldValue && log.newValue" class="arrow">→</span>
                  <span *ngIf="log.newValue" class="new-value">{{ log.newValue }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Comments Section -->
        <div class="bug-detail__comments">
          <app-bug-comments
            [bugId]="bug.id"
            [comments]="bug.comments"
          ></app-bug-comments>
        </div>

        <!-- Watchers Section -->
        <div class="bug-detail__watchers">
          <app-bug-watchers
            [bugId]="bug.id"
            [watchers]="bug.watchers"
          ></app-bug-watchers>
        </div>
      </div>

      <!-- Status Update Modal -->
      <div *ngIf="showStatusModal" class="modal-overlay" (click)="closeStatusModal()">
        <div class="modal-content" (click)="$event.stopPropagation()">
          <div class="modal-header">
            <h3>Update Bug Status</h3>
            <button class="modal-close" (click)="closeStatusModal()">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="18" y1="6" x2="6" y2="18"/>
                <line x1="6" y1="6" x2="18" y2="18"/>
              </svg>
            </button>
          </div>
          <form [formGroup]="statusForm" (ngSubmit)="updateStatus()" class="modal-body">
            <div class="form-group">
              <label for="newStatus" class="form-label">New Status</label>
              <select id="newStatus" class="form-select" formControlName="newStatus">
                <option *ngFor="let status of getAvailableStatuses()" [value]="status">
                  {{ status }}
                </option>
              </select>
            </div>
            <div class="form-group" *ngIf="statusForm.get('newStatus')?.value === 'Fixed'">
              <label for="actualFixDate" class="form-label">Actual Fix Date</label>
              <input
                id="actualFixDate"
                type="date"
                class="form-input"
                formControlName="actualFixDate"
              />
            </div>
            <div class="form-group" *ngIf="statusForm.get('newStatus')?.value === 'Fixed'">
              <label for="actualHours" class="form-label">Actual Hours</label>
              <input
                id="actualHours"
                type="number"
                class="form-input"
                formControlName="actualHours"
                min="0"
                step="0.5"
                placeholder="0.0"
              />
            </div>
            <div class="form-group">
              <label for="comment" class="form-label">Comment (Optional)</label>
              <textarea
                id="comment"
                class="form-textarea"
                formControlName="comment"
                rows="3"
                placeholder="Add a comment about this status change..."
              ></textarea>
            </div>
            <div class="modal-actions">
              <button type="button" class="btn btn-secondary" (click)="closeStatusModal()">
                Cancel
              </button>
              <button
                type="submit"
                class="btn btn-primary"
                [disabled]="statusForm.invalid || updatingStatus"
              >
                <span *ngIf="updatingStatus" class="btn-spinner"></span>
                Update Status
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .bug-detail {
      max-width: 1400px;
      margin: 0 auto;
      padding: var(--spacing-6);
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: var(--spacing-12);
      color: var(--color-gray-600);
    }

    .loading-spinner {
      width: 32px;
      height: 32px;
      border: 3px solid var(--color-gray-200);
      border-top: 3px solid var(--color-primary-500);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: var(--spacing-3);
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .error-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--spacing-6);
      padding: var(--spacing-12);
    }

    .error-banner {
      display: flex;
      align-items: center;
      gap: var(--spacing-4);
      padding: var(--spacing-6);
      background: var(--color-red-50);
      border: 1px solid var(--color-red-200);
      border-radius: var(--border-radius-lg);
      color: var(--color-red-800);
    }

    .error-icon {
      color: var(--color-red-500);
      flex-shrink: 0;
    }

    .error-banner h3 {
      margin: 0 0 var(--spacing-1) 0;
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
    }

    .error-banner p {
      margin: 0;
      font-size: var(--font-size-sm);
    }

    .error-actions {
      display: flex;
      gap: var(--spacing-3);
    }

    .bug-detail__content {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-6);
    }

    .bug-detail__header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: var(--spacing-4);
    }

    .bug-detail__title-section {
      flex: 1;
    }

    .bug-detail__breadcrumb {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      margin-bottom: var(--spacing-2);
      font-size: var(--font-size-sm);
    }

    .breadcrumb-link {
      color: var(--color-primary-600);
      text-decoration: none;
    }

    .breadcrumb-link:hover {
      text-decoration: underline;
    }

    .breadcrumb-separator {
      color: var(--color-gray-400);
    }

    .breadcrumb-current {
      color: var(--color-gray-600);
      font-weight: var(--font-weight-medium);
    }

    .bug-detail__title {
      font-size: var(--font-size-3xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-gray-900);
      margin: 0 0 var(--spacing-3) 0;
      line-height: 1.2;
    }

    .bug-detail__meta {
      display: flex;
      flex-wrap: wrap;
      gap: var(--spacing-4);
      font-size: var(--font-size-sm);
      color: var(--color-gray-600);
    }

    .meta-item strong {
      color: var(--color-gray-900);
    }

    .bug-detail__actions {
      display: flex;
      gap: var(--spacing-3);
    }

    .bug-detail__badges {
      display: flex;
      flex-wrap: wrap;
      gap: var(--spacing-2);
    }

    .badge {
      display: inline-flex;
      align-items: center;
      padding: var(--spacing-1) var(--spacing-3);
      border-radius: var(--border-radius-full);
      font-size: var(--font-size-xs);
      font-weight: var(--font-weight-medium);
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }

    .badge--info {
      background: var(--color-blue-100);
      color: var(--color-blue-800);
    }

    .badge--warning {
      background: var(--color-yellow-100);
      color: var(--color-yellow-800);
    }

    .badge--success {
      background: var(--color-green-100);
      color: var(--color-green-800);
    }

    .badge--error {
      background: var(--color-red-100);
      color: var(--color-red-800);
    }

    .badge--gray {
      background: var(--color-gray-100);
      color: var(--color-gray-800);
    }

    .bug-detail__grid {
      display: grid;
      grid-template-columns: 1fr 350px;
      gap: var(--spacing-8);
    }

    @media (max-width: 1024px) {
      .bug-detail__grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-6);
      }
    }

    .bug-detail__main {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-6);
    }

    .detail-section {
      background: var(--color-white);
      border: 1px solid var(--color-gray-200);
      border-radius: var(--border-radius-lg);
      overflow: hidden;
    }

    .section-title {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-900);
      margin: 0;
      padding: var(--spacing-4) var(--spacing-6);
      background: var(--color-gray-50);
      border-bottom: 1px solid var(--color-gray-200);
    }

    .section-content {
      padding: var(--spacing-6);
    }

    .description-text {
      margin: 0;
      line-height: 1.6;
      color: var(--color-gray-700);
    }

    .steps-text {
      margin: 0;
      font-family: var(--font-family-mono);
      font-size: var(--font-size-sm);
      line-height: 1.6;
      color: var(--color-gray-700);
      white-space: pre-wrap;
      background: var(--color-gray-50);
      padding: var(--spacing-4);
      border-radius: var(--border-radius-md);
    }

    .tags-container {
      display: flex;
      flex-wrap: wrap;
      gap: var(--spacing-2);
    }

    .tag {
      display: inline-flex;
      align-items: center;
      padding: var(--spacing-1) var(--spacing-2);
      background: var(--color-primary-100);
      color: var(--color-primary-800);
      border-radius: var(--border-radius-md);
      font-size: var(--font-size-xs);
      font-weight: var(--font-weight-medium);
    }

    .bug-detail__sidebar {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-4);
    }

    .sidebar-section {
      background: var(--color-white);
      border: 1px solid var(--color-gray-200);
      border-radius: var(--border-radius-lg);
      padding: var(--spacing-4);
    }

    .sidebar-title {
      font-size: var(--font-size-md);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-900);
      margin: 0 0 var(--spacing-3) 0;
    }

    .status-actions {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-2);
    }

    .status-help {
      font-size: var(--font-size-xs);
      color: var(--color-gray-500);
      font-style: italic;
    }

    .info-grid,
    .assignment-grid,
    .timeline-grid,
    .effort-grid,
    .stats-grid {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-2);
    }

    .info-item,
    .assignment-item,
    .timeline-item,
    .effort-item,
    .stat-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--spacing-2) 0;
      border-bottom: 1px solid var(--color-gray-100);
    }

    .info-item:last-child,
    .assignment-item:last-child,
    .timeline-item:last-child,
    .effort-item:last-child,
    .stat-item:last-child {
      border-bottom: none;
    }

    .info-item label,
    .assignment-item label,
    .timeline-item label,
    .effort-item label,
    .stat-item label {
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-600);
    }

    .info-item span,
    .assignment-item span,
    .timeline-item span,
    .effort-item span,
    .stat-item span {
      font-size: var(--font-size-sm);
      color: var(--color-gray-900);
    }

    .unassigned {
      color: var(--color-gray-500);
      font-style: italic;
    }

    .overdue {
      color: var(--color-red-600);
      font-weight: var(--font-weight-medium);
    }

    .bug-detail__activity {
      margin-top: var(--spacing-8);
    }

    .activity-title {
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-900);
      margin: 0 0 var(--spacing-4) 0;
    }

    .activity-timeline {
      background: var(--color-white);
      border: 1px solid var(--color-gray-200);
      border-radius: var(--border-radius-lg);
      overflow: hidden;
    }

    .activity-item {
      display: flex;
      gap: var(--spacing-3);
      padding: var(--spacing-4);
      border-bottom: 1px solid var(--color-gray-100);
    }

    .activity-item:last-child {
      border-bottom: none;
    }

    .activity-icon {
      flex-shrink: 0;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--color-primary-100);
      color: var(--color-primary-600);
      border-radius: 50%;
    }

    .activity-content {
      flex: 1;
    }

    .activity-header {
      display: flex;
      flex-wrap: wrap;
      gap: var(--spacing-2);
      margin-bottom: var(--spacing-1);
    }

    .activity-user {
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-900);
    }

    .activity-action {
      color: var(--color-gray-700);
    }

    .activity-time {
      font-size: var(--font-size-xs);
      color: var(--color-gray-500);
      margin-left: auto;
    }

    .activity-details {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      font-size: var(--font-size-sm);
    }

    .old-value {
      color: var(--color-red-600);
      text-decoration: line-through;
    }

    .arrow {
      color: var(--color-gray-400);
    }

    .new-value {
      color: var(--color-green-600);
      font-weight: var(--font-weight-medium);
    }

    .bug-detail__comments {
      background: var(--color-white);
      border: 1px solid var(--color-gray-200);
      border-radius: var(--border-radius-lg);
      padding: var(--spacing-6);
    }

    .bug-detail__watchers {
      background: var(--color-white);
      border: 1px solid var(--color-gray-200);
      border-radius: var(--border-radius-lg);
      padding: var(--spacing-6);
    }

    /* Modal Styles */
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
      padding: var(--spacing-4);
    }

    .modal-content {
      background: var(--color-white);
      border-radius: var(--border-radius-lg);
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
      max-width: 500px;
      width: 100%;
      max-height: 90vh;
      overflow-y: auto;
    }

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--spacing-6);
      border-bottom: 1px solid var(--color-gray-200);
    }

    .modal-header h3 {
      margin: 0;
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-900);
    }

    .modal-close {
      background: none;
      border: none;
      color: var(--color-gray-400);
      cursor: pointer;
      padding: var(--spacing-1);
      border-radius: var(--border-radius-sm);
      transition: color 0.2s ease;
    }

    .modal-close:hover {
      color: var(--color-gray-600);
    }

    .modal-body {
      padding: var(--spacing-6);
    }

    .form-group {
      margin-bottom: var(--spacing-4);
    }

    .form-label {
      display: block;
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-700);
      margin-bottom: var(--spacing-1);
    }

    .form-input,
    .form-select,
    .form-textarea {
      width: 100%;
      padding: var(--spacing-3);
      border: 1px solid var(--color-gray-300);
      border-radius: var(--border-radius-md);
      font-size: var(--font-size-sm);
      background: var(--color-white);
      transition: border-color 0.2s ease, box-shadow 0.2s ease;
    }

    .form-input:focus,
    .form-select:focus,
    .form-textarea:focus {
      outline: none;
      border-color: var(--color-primary-500);
      box-shadow: 0 0 0 3px var(--color-primary-100);
    }

    .form-textarea {
      resize: vertical;
      min-height: 80px;
    }

    .modal-actions {
      display: flex;
      justify-content: flex-end;
      gap: var(--spacing-3);
      margin-top: var(--spacing-6);
    }

    .btn-spinner {
      display: inline-block;
      width: 16px;
      height: 16px;
      border: 2px solid transparent;
      border-top: 2px solid currentColor;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: var(--spacing-2);
    }

    @media (max-width: 768px) {
      .bug-detail {
        padding: var(--spacing-4);
      }

      .bug-detail__header {
        flex-direction: column;
        align-items: stretch;
      }

      .bug-detail__meta {
        flex-direction: column;
        gap: var(--spacing-2);
      }

      .modal-overlay {
        padding: var(--spacing-2);
      }

      .modal-header,
      .modal-body {
        padding: var(--spacing-4);
      }

      .modal-actions {
        flex-direction: column;
      }
    }
  `]
})
export class BugDetailComponent implements OnInit {
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private bugService = inject(BugService);
  private userService = inject(UserService);
  private authService = inject(AuthService);
  private fb = inject(FormBuilder);

  // Data
  bug: Bug | null = null;
  currentUser: AuthUser | null = null;

  // State
  loading = false;
  errorMessage = '';
  updatingStatus = false;
  showStatusModal = false;

  // Forms
  statusForm!: FormGroup;

  // Route params
  bugId: string | null = null;

  ngOnInit() {
    this.currentUser = this.authService.getCurrentUser();
    this.initializeStatusForm();
    this.loadBugFromRoute();
    this.subscribeToCommentChanges();
  }

  private subscribeToCommentChanges() {
    // Subscribe to bug changes to refresh comments and watchers when they're added/updated/deleted
    this.bugService.bugs$.subscribe(bugs => {
      if (this.bugId && this.bug) {
        const updatedBug = bugs.find(b => b.id === this.bugId);
        if (updatedBug && (
          updatedBug.comments.length !== this.bug.comments.length ||
          updatedBug.watchers.length !== this.bug.watchers.length
        )) {
          this.bug = updatedBug;
        }
      }
    });
  }

  private initializeStatusForm() {
    this.statusForm = this.fb.group({
      newStatus: ['', [Validators.required]],
      actualFixDate: [''],
      actualHours: [''],
      comment: ['']
    });
  }

  private loadBugFromRoute() {
    this.bugId = this.route.snapshot.paramMap.get('id');
    if (this.bugId) {
      this.loadBug();
    } else {
      this.errorMessage = 'Bug ID not found in URL';
    }
  }

  loadBug() {
    if (!this.bugId) return;

    this.loading = true;
    this.errorMessage = '';

    this.bugService.getBugById(this.bugId).subscribe({
      next: (bug) => {
        if (bug) {
          this.bug = bug;
        } else {
          this.errorMessage = 'Bug not found';
        }
        this.loading = false;
      },
      error: (error) => {
        this.errorMessage = error.message || 'Failed to load bug details';
        this.loading = false;
      }
    });
  }

  // Navigation methods
  goBack() {
    this.router.navigate(['/bugs']);
  }

  // Permission methods
  canEditBug(): boolean {
    if (!this.currentUser || !this.bug) return false;

    // Admin can edit any bug
    if (this.currentUser.role === UserRole.ADMIN) return true;

    // Reporter can edit their own bugs
    if (this.bug.reportedById === this.currentUser.id) return true;

    // Assigned users can edit
    if (this.bug.qaAssignedId === this.currentUser.id ||
        this.bug.devAssignedId === this.currentUser.id) return true;

    return false;
  }

  canChangeStatus(): boolean {
    if (!this.currentUser || !this.bug) return false;
    return this.getAvailableStatuses().length > 0;
  }

  // Status management
  getAvailableStatuses(): BugStatus[] {
    if (!this.bug || !this.currentUser) return [];
    return getNextValidStatuses(this.bug.status, this.currentUser.role);
  }

  onStatusChange(event: Event) {
    const target = event.target as HTMLSelectElement;
    const newStatus = target.value as BugStatus;

    if (newStatus && newStatus !== this.bug?.status) {
      this.statusForm.patchValue({ newStatus });
      this.showStatusModal = true;
    }
  }

  closeStatusModal() {
    this.showStatusModal = false;
    this.statusForm.reset();
  }

  updateStatus() {
    if (!this.bug || !this.bugId || this.statusForm.invalid) return;

    this.updatingStatus = true;
    const formValue = this.statusForm.value;

    const updateRequest = {
      status: formValue.newStatus,
      actualFixDate: formValue.actualFixDate ? new Date(formValue.actualFixDate) : undefined,
      actualHours: formValue.actualHours || undefined
    };

    this.bugService.updateBug(this.bugId, updateRequest).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.bug = response.data;
          this.closeStatusModal();
        } else {
          this.errorMessage = response.message || 'Failed to update status';
        }
        this.updatingStatus = false;
      },
      error: (error) => {
        this.errorMessage = error.message || 'Failed to update status';
        this.updatingStatus = false;
      }
    });
  }

  // Utility methods
  getBugStatusColor(status: BugStatus): string {
    return getBugStatusColor(status);
  }

  getBugSeverityColor(severity: BugSeverity): string {
    return getBugSeverityColor(severity);
  }

  getBugPriorityColor(priority: BugPriority): string {
    return getBugPriorityColor(priority);
  }

  formatDate(date: Date): string {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  isOverdue(): boolean {
    if (!this.bug?.expectedFixDate) return false;
    return new Date() > new Date(this.bug.expectedFixDate) &&
           this.bug.status !== BugStatus.CLOSED;
  }

  getDaysOpen(): number {
    if (!this.bug) return 0;
    const now = new Date();
    const issueDate = new Date(this.bug.issueDate);
    const diffTime = Math.abs(now.getTime() - issueDate.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  trackByLogId(index: number, log: BugAuditLog): string {
    return log.id;
  }

  // Additional methods for compatibility with tests
  onStatusUpdate() {
    // This method is called by tests but the actual status update is handled by updateStatus()
    this.updateStatus();
  }

  deleteBug() {
    if (!this.bug || !confirm('Are you sure you want to delete this bug? This action cannot be undone.')) {
      return;
    }

    this.bugService.deleteBug(this.bug.id).subscribe({
      next: (response) => {
        if (response.success) {
          this.router.navigate(['/bugs']);
        } else {
          this.errorMessage = response.message || 'Failed to delete bug';
        }
      },
      error: (error) => {
        this.errorMessage = 'An error occurred while deleting the bug';
        console.error('Delete bug error:', error);
      }
    });
  }
}
