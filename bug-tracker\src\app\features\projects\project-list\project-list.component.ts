import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { ProjectService } from '../../../core/services/project.service';
import { UserPreferencesService, ViewMode } from '../../../core/services/user-preferences.service';
import { Project, ProjectStatus, ProjectFilter, ProjectSummary } from '../../../core/models/project.model';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-project-list',
  standalone: true,
  imports: [CommonModule, RouterModule, FormsModule],
  template: `
    <div class="page-container">
      <div class="page-header">
        <div class="project-list__header">
          <h1>Projects</h1>
          <a routerLink="/projects/create" class="btn btn-primary">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <rect x="11" y="4" width="2" height="16" rx="1"></rect>
              <rect x="4" y="11" width="16" height="2" rx="1"></rect>
            </svg>
            Create Project
          </a>
        </div>
      </div>

      <!-- Filters -->
      <div class="project-list__filters">
        <div class="filters-row">
          <div class="filter-group">
            <input
              type="text"
              class="form-control"
              placeholder="Search projects..."
              [(ngModel)]="searchTerm"
              (input)="onSearch()">
          </div>
          <div class="filter-group">
            <select class="form-control" [(ngModel)]="selectedStatus" (change)="onFilterChange()">
              <option value="">All Status</option>
              <option value="Planning">Planning</option>
              <option value="Active">Active</option>
              <option value="On Hold">On Hold</option>
              <option value="Completed">Completed</option>
              <option value="Archived">Archived</option>
            </select>
          </div>
          <div class="view-toggle">
            <button
              class="btn-toggle"
              [class.active]="viewMode === 'grid'"
              (click)="setViewMode('grid')"
              title="Grid View">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <rect x="3" y="3" width="7" height="7" rx="1"/>
                <rect x="14" y="3" width="7" height="7" rx="1"/>
                <rect x="3" y="14" width="7" height="7" rx="1"/>
                <rect x="14" y="14" width="7" height="7" rx="1"/>
              </svg>
            </button>
            <button
              class="btn-toggle"
              [class.active]="viewMode === 'table'"
              (click)="setViewMode('table')"
              title="Table View">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M3 6h18v2H3V6zm0 5h18v2H3v-2zm0 5h18v2H3v-2z"/>
              </svg>
            </button>
          </div>
          <div class="filter-group">
            <button class="btn btn-outline" (click)="clearFilters()">Clear Filters</button>
          </div>
        </div>
      </div>

      <!-- Project Content -->
      <div class="page-content">
        <!-- Grid View -->
        <div class="project-grid" *ngIf="viewMode === 'grid' && projects.length > 0">
          <div class="project-card" *ngFor="let project of projects" [routerLink]="['/projects', project.id]">
            <div class="project-card__header">
              <div class="project-card__title">
                <h3>{{ project.name }}</h3>
                <span class="project-status" [class]="'project-status--' + getStatusClass(project.status)">
                  {{ project.status }}
                </span>
              </div>
              <div class="project-card__actions">
                <button class="btn-icon" [routerLink]="['/projects', project.id, 'edit']" (click)="$event.stopPropagation()">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                  </svg>
                </button>
              </div>
            </div>

            <div class="project-card__content">
              <p class="project-description">{{ project.description }}</p>

              <div class="project-stats">
                <div class="stat-item">
                  <span class="stat-label">Modules</span>
                  <span class="stat-value">{{ project.totalModules }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">Features</span>
                  <span class="stat-value">{{ project.totalFeatures }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">Team</span>
                  <span class="stat-value">{{ project.totalTeamMembers }}</span>
                </div>
              </div>

              <div class="project-progress">
                <div class="progress-bar">
                  <div class="progress-fill" [style.width.%]="getProjectProgress(project.status)"></div>
                </div>
                <span class="progress-text">{{ getProjectProgress(project.status) }}% Complete</span>
              </div>
            </div>

            <div class="project-card__footer">
              <div class="project-team">
                <div class="team-info">
                  <span class="team-count">{{ project.totalTeamMembers }} team members</span>
                </div>
              </div>
              <div class="project-date">
                <small>Updated {{ formatDate(project.updatedAt) }}</small>
              </div>
            </div>
          </div>
        </div>

        <!-- Table View -->
        <div class="project-table-container" *ngIf="viewMode === 'table' && projects.length > 0">
          <table class="project-table">
            <thead>
              <tr>
                <th>Project Name</th>
                <th>Status</th>
                <th>Description</th>
                <th>Modules</th>
                <th>Features</th>
                <th>Team Members</th>
                <th>Progress</th>
                <th>Created</th>
                <th>Updated</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let project of projects" class="project-row" [routerLink]="['/projects', project.id]">
                <td class="project-name">
                  <div class="name-cell">
                    <h4>{{ project.name }}</h4>
                  </div>
                </td>
                <td class="project-status-cell">
                  <span class="project-status" [class]="'project-status--' + getStatusClass(project.status)">
                    {{ project.status }}
                  </span>
                </td>
                <td class="project-description-cell">
                  <p class="description-text">{{ project.description | slice:0:100 }}{{ project.description.length > 100 ? '...' : '' }}</p>
                </td>
                <td class="stat-cell">{{ project.totalModules }}</td>
                <td class="stat-cell">{{ project.totalFeatures }}</td>
                <td class="stat-cell">{{ project.totalTeamMembers }}</td>
                <td class="progress-cell">
                  <div class="table-progress">
                    <div class="progress-bar">
                      <div class="progress-fill" [style.width.%]="getProjectProgress(project.status)"></div>
                    </div>
                    <span class="progress-text">{{ getProjectProgress(project.status) }}%</span>
                  </div>
                </td>
                <td class="date-cell">{{ formatDate(project.createdAt) }}</td>
                <td class="date-cell">{{ formatDate(project.updatedAt) }}</td>
                <td class="actions-cell">
                  <button class="btn-icon" [routerLink]="['/projects', project.id, 'edit']" (click)="$event.stopPropagation()" title="Edit Project">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                    </svg>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- No Projects State -->
        <div class="empty-state" *ngIf="projects.length === 0">
          <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1">
            <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 2 0 0 1 2 2z"></path>
          </svg>
          <h3>No Projects Found</h3>
          <p>{{ searchTerm || selectedStatus ? 'No projects match your current filters.' : 'Get started by creating your first project.' }}</p>
          <a routerLink="/projects/create" class="btn btn-primary">Create Project</a>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .project-list__header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .project-list__header h1 {
      font-size: var(--font-size-3xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-gray-900);
      margin: 0;
    }

    /* Filters */
    .project-list__filters {
      margin-bottom: var(--spacing-6);
    }

    .filters-row {
      display: flex;
      gap: var(--spacing-4);
      align-items: center;
      flex-wrap: wrap;
    }

    .view-toggle {
      display: flex;
      gap: var(--spacing-1);
      background: var(--color-gray-100);
      border-radius: var(--border-radius-md);
      padding: var(--spacing-1);
    }

    .btn-toggle {
      background: none;
      border: none;
      padding: var(--spacing-2);
      border-radius: var(--border-radius-sm);
      cursor: pointer;
      color: var(--color-gray-600);
      transition: all var(--transition-fast);
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background: var(--color-white);
        color: var(--color-gray-800);
      }

      &.active {
        background: var(--color-white);
        color: var(--color-primary-600);
        box-shadow: var(--shadow-sm);
      }
    }

    .filter-group {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-2);
    }

    .filter-group .form-control {
      min-width: 200px;
    }

    /* Project Grid */
    .project-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: var(--spacing-6);
    }

    .project-card {
      background: var(--color-white);
      border: 1px solid var(--color-gray-200);
      border-radius: var(--border-radius-lg);
      padding: var(--spacing-6);
      cursor: pointer;
      transition: all var(--transition-base);
      text-decoration: none;
      color: inherit;
    }

    .project-card:hover {
      border-color: var(--color-primary-300);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .project-card__header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: var(--spacing-4);
    }

    .project-card__title h3 {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-900);
      margin: 0 0 var(--spacing-2) 0;
    }

    .project-status {
      display: inline-block;
      padding: var(--spacing-1) var(--spacing-3);
      border-radius: var(--border-radius-full);
      font-size: var(--font-size-xs);
      font-weight: var(--font-weight-medium);
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }

    .project-status--active {
      background-color: var(--color-success-100);
      color: var(--color-success-700);
    }

    .project-status--planning {
      background-color: var(--color-info-100);
      color: var(--color-info-700);
    }

    .project-status--completed {
      background-color: var(--color-primary-100);
      color: var(--color-primary-700);
    }

    .project-status--on-hold {
      background-color: var(--color-warning-100);
      color: var(--color-warning-700);
    }

    .project-status--archived {
      background-color: var(--color-gray-100);
      color: var(--color-gray-700);
    }

    .project-card__actions {
      display: flex;
      gap: var(--spacing-2);
    }

    .btn-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border: 1px solid var(--color-gray-300);
      border-radius: var(--border-radius-md);
      background: var(--color-white);
      color: var(--color-gray-600);
      cursor: pointer;
      transition: all var(--transition-fast);
    }

    .btn-icon:hover {
      border-color: var(--color-primary-300);
      color: var(--color-primary-600);
      background: var(--color-primary-50);
    }

    .project-description {
      color: var(--color-gray-600);
      font-size: var(--font-size-sm);
      line-height: 1.5;
      margin-bottom: var(--spacing-4);
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .project-stats {
      display: flex;
      gap: var(--spacing-4);
      margin-bottom: var(--spacing-4);
    }

    .stat-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
    }

    .stat-label {
      font-size: var(--font-size-xs);
      color: var(--color-gray-500);
      margin-bottom: var(--spacing-1);
    }

    .stat-value {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-900);
    }

    .project-progress {
      margin-bottom: var(--spacing-4);
    }

    .progress-bar {
      width: 100%;
      height: 6px;
      background-color: var(--color-gray-200);
      border-radius: var(--border-radius-full);
      overflow: hidden;
      margin-bottom: var(--spacing-2);
    }

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, var(--color-primary-500), var(--color-primary-600));
      border-radius: var(--border-radius-full);
      transition: width var(--transition-base);
    }

    .progress-text {
      font-size: var(--font-size-xs);
      color: var(--color-gray-600);
    }

    .project-card__footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: var(--spacing-4);
      border-top: 1px solid var(--color-gray-100);
    }

    .team-avatars {
      display: flex;
      gap: var(--spacing-1);
    }

    .avatar {
      width: 32px;
      height: 32px;
      border-radius: var(--border-radius-full);
      background: var(--color-primary-100);
      color: var(--color-primary-700);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: var(--font-size-xs);
      font-weight: var(--font-weight-medium);
      border: 2px solid var(--color-white);
    }

    .avatar--more {
      background: var(--color-gray-100);
      color: var(--color-gray-600);
    }

    .project-date {
      color: var(--color-gray-500);
      font-size: var(--font-size-xs);
    }

    /* Table View */
    .project-table-container {
      background: var(--color-white);
      border: 1px solid var(--color-gray-200);
      border-radius: var(--border-radius-lg);
      overflow: hidden;
    }

    .project-table {
      width: 100%;
      border-collapse: collapse;
    }

    .project-table th {
      background: var(--color-gray-50);
      padding: var(--spacing-4);
      text-align: left;
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-700);
      font-size: var(--font-size-sm);
      border-bottom: 1px solid var(--color-gray-200);
      white-space: nowrap;
    }

    .project-table td {
      padding: var(--spacing-4);
      border-bottom: 1px solid var(--color-gray-100);
      vertical-align: middle;
    }

    .project-row {
      cursor: pointer;
      transition: background-color var(--transition-fast);

      &:hover {
        background: var(--color-gray-50);
      }

      &:last-child td {
        border-bottom: none;
      }
    }

    .project-name h4 {
      margin: 0;
      font-size: var(--font-size-base);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-900);
    }

    .project-status-cell .project-status {
      font-size: var(--font-size-xs);
      padding: var(--spacing-1) var(--spacing-2);
    }

    .description-text {
      margin: 0;
      color: var(--color-gray-600);
      font-size: var(--font-size-sm);
      line-height: 1.4;
    }

    .stat-cell {
      text-align: center;
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-700);
    }

    .progress-cell {
      min-width: 120px;
    }

    .table-progress {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
    }

    .table-progress .progress-bar {
      flex: 1;
      height: 6px;
    }

    .table-progress .progress-text {
      font-size: var(--font-size-xs);
      color: var(--color-gray-600);
      white-space: nowrap;
    }

    .date-cell {
      color: var(--color-gray-600);
      font-size: var(--font-size-sm);
      white-space: nowrap;
    }

    .actions-cell {
      width: 60px;
      text-align: center;
    }

    /* Empty State */
    .empty-state {
      text-align: center;
      padding: var(--spacing-12) var(--spacing-6);
      color: var(--color-gray-500);
    }

    .empty-state svg {
      margin-bottom: var(--spacing-4);
      color: var(--color-gray-400);
    }

    .empty-state h3 {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-700);
      margin: 0 0 var(--spacing-2) 0;
    }

    .empty-state p {
      margin-bottom: var(--spacing-6);
      max-width: 400px;
      margin-left: auto;
      margin-right: auto;
    }

    /* Responsive */
    @media (max-width: 768px) {
      .project-grid {
        grid-template-columns: 1fr;
      }

      .filters-row {
        flex-direction: column;
        align-items: stretch;
      }

      .filter-group .form-control {
        min-width: auto;
      }

      .project-stats {
        justify-content: space-around;
      }

      /* Table responsive behavior */
      .project-table-container {
        overflow-x: auto;
      }

      .project-table {
        min-width: 800px;
      }

      .project-table th,
      .project-table td {
        padding: var(--spacing-2) var(--spacing-3);
      }

      .description-text {
        max-width: 200px;
      }
    }
  `]
})
export class ProjectListComponent implements OnInit {
  private projectService = inject(ProjectService);
  private userPreferencesService = inject(UserPreferencesService);

  allProjects: ProjectSummary[] = [];
  filteredProjects: ProjectSummary[] = [];
  searchTerm = '';
  selectedStatus = '';
  loading = false;
  viewMode: ViewMode = 'grid';

  ngOnInit() {
    this.loadProjects();
    this.loadViewMode();
  }

  loadProjects() {
    this.loading = true;
    this.projectService.getProjects().subscribe({
      next: (response) => {
        this.allProjects = response.projects;
        this.filteredProjects = [...this.allProjects];
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading projects:', error);
        this.loading = false;
      }
    });
  }

  onSearch() {
    this.applyFilters();
  }

  onFilterChange() {
    this.applyFilters();
  }

  clearFilters() {
    this.searchTerm = '';
    this.selectedStatus = '';
    this.applyFilters();
  }

  private applyFilters() {
    let filtered = [...this.allProjects];

    // Apply search filter
    if (this.searchTerm.trim()) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(project =>
        project.name.toLowerCase().includes(term) ||
        project.description.toLowerCase().includes(term)
      );
    }

    // Apply status filter
    if (this.selectedStatus) {
      filtered = filtered.filter(project => project.status === this.selectedStatus);
    }

    this.filteredProjects = filtered;
  }

  get projects() {
    return this.filteredProjects;
  }

  getProjectProgress(status: ProjectStatus): number {
    // Simple progress calculation based on status
    switch (status) {
      case ProjectStatus.COMPLETED:
        return 100;
      case ProjectStatus.ACTIVE:
        return 70;
      case ProjectStatus.PLANNING:
        return 10;
      case ProjectStatus.ON_HOLD:
        return 45;
      case ProjectStatus.ARCHIVED:
        return 100;
      default:
        return 0;
    }
  }

  getStatusClass(status: ProjectStatus): string {
    switch (status) {
      case ProjectStatus.ACTIVE:
        return 'active';
      case ProjectStatus.COMPLETED:
        return 'completed';
      case ProjectStatus.PLANNING:
        return 'planning';
      case ProjectStatus.ON_HOLD:
        return 'on-hold';
      case ProjectStatus.ARCHIVED:
        return 'archived';
      default:
        return 'default';
    }
  }



  formatDate(date: Date): string {
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
    return date.toLocaleDateString();
  }

  setViewMode(mode: ViewMode) {
    this.viewMode = mode;
    this.userPreferencesService.setProjectsViewMode(mode);
  }

  private loadViewMode() {
    this.viewMode = this.userPreferencesService.getProjectsViewMode();
  }
}
