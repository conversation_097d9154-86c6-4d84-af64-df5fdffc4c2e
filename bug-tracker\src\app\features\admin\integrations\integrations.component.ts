import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';

interface Integration {
  id: string;
  name: string;
  displayName: string;
  description: string;
  icon: string;
  category: 'communication' | 'project-management' | 'development' | 'monitoring';
  status: 'connected' | 'disconnected' | 'error' | 'configuring';
  isEnabled: boolean;
  config: any;
  lastSync?: Date;
  features: string[];
  setupSteps: string[];
}

@Component({
  selector: 'app-integrations',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, FormsModule],
  template: `
    <div class="integrations">
      <!-- Header -->
      <div class="integrations__header">
        <div class="integrations__title-section">
          <h1 class="integrations__title">Integrations</h1>
          <p class="integrations__subtitle">Connect with external services to enhance your workflow</p>
        </div>
        <div class="integrations__stats">
          <div class="stat-card">
            <div class="stat-value">{{ getConnectedCount() }}</div>
            <div class="stat-label">Connected</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ integrations.length }}</div>
            <div class="stat-label">Available</div>
          </div>
        </div>
      </div>

      <!-- Category Filter -->
      <div class="integrations__filters">
        <div class="filter-tabs">
          <button
            *ngFor="let category of categories"
            class="filter-tab"
            [class.filter-tab--active]="selectedCategory === category.key"
            (click)="selectCategory(category.key)"
          >
            <div class="filter-tab__icon" [innerHTML]="category.icon"></div>
            <span>{{ category.label }}</span>
          </button>
        </div>
      </div>

      <!-- Loading State -->
      <div *ngIf="loading" class="loading-state">
        <div class="loading-spinner"></div>
        <p>Loading integrations...</p>
      </div>

      <!-- Integrations Grid -->
      <div *ngIf="!loading" class="integrations-grid">
        <div *ngFor="let integration of filteredIntegrations; trackBy: trackByIntegration"
             class="integration-card"
             [class]="'integration-card--' + integration.status">

          <!-- Card Header -->
          <div class="integration-card__header">
            <div class="integration-card__icon" [innerHTML]="integration.icon"></div>
            <div class="integration-card__info">
              <h3 class="integration-card__title">{{ integration.displayName }}</h3>
              <p class="integration-card__description">{{ integration.description }}</p>
            </div>
            <div class="integration-card__status">
              <span class="status-badge" [class]="'status-' + integration.status">
                {{ getStatusLabel(integration.status) }}
              </span>
            </div>
          </div>

          <!-- Features -->
          <div class="integration-card__features">
            <div class="features-title">Features</div>
            <div class="features-list">
              <span *ngFor="let feature of integration.features" class="feature-tag">
                {{ feature }}
              </span>
            </div>
          </div>

          <!-- Last Sync -->
          <div *ngIf="integration.lastSync && integration.status === 'connected'" class="integration-card__sync">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polyline points="23 4 23 10 17 10"></polyline>
              <polyline points="1 20 1 14 7 14"></polyline>
              <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"></path>
            </svg>
            <span>Last sync: {{ integration.lastSync | date:'short' }}</span>
          </div>

          <!-- Actions -->
          <div class="integration-card__actions">
            <button
              *ngIf="integration.status === 'disconnected'"
              class="btn btn-primary btn-sm"
              (click)="configureIntegration(integration)"
            >
              Connect
            </button>

            <button
              *ngIf="integration.status === 'connected'"
              class="btn btn-outline btn-sm"
              (click)="configureIntegration(integration)"
            >
              Configure
            </button>

            <button
              *ngIf="integration.status === 'connected'"
              class="btn btn-outline btn-sm"
              (click)="testConnection(integration)"
              [disabled]="testing === integration.id"
            >
              <span *ngIf="testing === integration.id" class="btn-spinner"></span>
              Test
            </button>

            <button
              *ngIf="integration.status === 'connected'"
              class="btn btn-danger btn-sm"
              (click)="disconnectIntegration(integration)"
            >
              Disconnect
            </button>

            <button
              *ngIf="integration.status === 'error'"
              class="btn btn-warning btn-sm"
              (click)="configureIntegration(integration)"
            >
              Fix Configuration
            </button>
          </div>
        </div>
      </div>

      <!-- Configuration Modal -->
      <div *ngIf="showConfigModal" class="modal-overlay" (click)="closeConfigModal()">
        <div class="modal-content modal-content--large" (click)="$event.stopPropagation()">
          <div class="modal-header">
            <h3>Configure {{ selectedIntegration?.displayName }}</h3>
            <button class="modal-close" (click)="closeConfigModal()">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="18" y1="6" x2="6" y2="18"/>
                <line x1="6" y1="6" x2="18" y2="18"/>
              </svg>
            </button>
          </div>

          <div class="modal-body" *ngIf="selectedIntegration">
            <!-- Setup Steps -->
            <div class="setup-steps">
              <h4>Setup Instructions</h4>
              <ol class="steps-list">
                <li *ngFor="let step of selectedIntegration.setupSteps">{{ step }}</li>
              </ol>
            </div>

            <!-- Configuration Form -->
            <form [formGroup]="configForm" (ngSubmit)="saveConfiguration()" class="config-form">
              <div *ngIf="selectedIntegration.name === 'slack'" class="form-section">
                <h4>Slack Configuration</h4>
                <div class="form-group">
                  <label for="slackWebhook" class="form-label">Webhook URL *</label>
                  <input
                    id="slackWebhook"
                    type="url"
                    class="form-input"
                    formControlName="webhookUrl"
                    placeholder="https://hooks.slack.com/services/..."
                  />
                  <div class="form-help">Get this from your Slack app's Incoming Webhooks section</div>
                </div>
                <div class="form-group">
                  <label for="slackChannel" class="form-label">Default Channel</label>
                  <input
                    id="slackChannel"
                    type="text"
                    class="form-input"
                    formControlName="channel"
                    placeholder="#general"
                  />
                </div>
              </div>

              <div *ngIf="selectedIntegration.name === 'teams'" class="form-section">
                <h4>Microsoft Teams Configuration</h4>
                <div class="form-group">
                  <label for="teamsWebhook" class="form-label">Webhook URL *</label>
                  <input
                    id="teamsWebhook"
                    type="url"
                    class="form-input"
                    formControlName="webhookUrl"
                    placeholder="https://outlook.office.com/webhook/..."
                  />
                  <div class="form-help">Create an Incoming Webhook connector in your Teams channel</div>
                </div>
              </div>

              <div *ngIf="selectedIntegration.name === 'jira'" class="form-section">
                <h4>JIRA Configuration</h4>
                <div class="form-row">
                  <div class="form-group">
                    <label for="jiraUrl" class="form-label">JIRA URL *</label>
                    <input
                      id="jiraUrl"
                      type="url"
                      class="form-input"
                      formControlName="baseUrl"
                      placeholder="https://yourcompany.atlassian.net"
                    />
                  </div>
                  <div class="form-group">
                    <label for="jiraProject" class="form-label">Project Key *</label>
                    <input
                      id="jiraProject"
                      type="text"
                      class="form-input"
                      formControlName="projectKey"
                      placeholder="PROJ"
                    />
                  </div>
                </div>
                <div class="form-row">
                  <div class="form-group">
                    <label for="jiraUsername" class="form-label">Username/Email *</label>
                    <input
                      id="jiraUsername"
                      type="text"
                      class="form-input"
                      formControlName="username"
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div class="form-group">
                    <label for="jiraToken" class="form-label">API Token *</label>
                    <input
                      id="jiraToken"
                      type="password"
                      class="form-input"
                      formControlName="apiToken"
                      placeholder="Your JIRA API token"
                    />
                  </div>
                </div>
              </div>

              <div *ngIf="selectedIntegration.name === 'github'" class="form-section">
                <h4>GitHub Configuration</h4>
                <div class="form-group">
                  <label for="githubToken" class="form-label">Personal Access Token *</label>
                  <input
                    id="githubToken"
                    type="password"
                    class="form-input"
                    formControlName="accessToken"
                    placeholder="ghp_..."
                  />
                  <div class="form-help">Generate a token with repo and issues permissions</div>
                </div>
                <div class="form-group">
                  <label for="githubRepo" class="form-label">Repository *</label>
                  <input
                    id="githubRepo"
                    type="text"
                    class="form-input"
                    formControlName="repository"
                    placeholder="owner/repository"
                  />
                </div>
              </div>

              <!-- Enable/Disable Toggle -->
              <div class="form-section">
                <div class="form-group">
                  <div class="toggle-switch">
                    <input
                      id="enableIntegration"
                      type="checkbox"
                      class="toggle-input"
                      formControlName="enabled"
                    />
                    <label for="enableIntegration" class="toggle-label">
                      <span class="toggle-slider"></span>
                    </label>
                    <span class="toggle-text">
                      {{ configForm.get('enabled')?.value ? 'Enabled' : 'Disabled' }}
                    </span>
                  </div>
                </div>
              </div>

              <div class="modal-actions">
                <button type="button" class="btn btn-secondary" (click)="closeConfigModal()">
                  Cancel
                </button>
                <button type="submit" class="btn btn-primary" [disabled]="configForm.invalid || saving">
                  <span *ngIf="saving" class="btn-spinner"></span>
                  {{ saving ? 'Saving...' : 'Save Configuration' }}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .integrations {
      padding: var(--spacing-6);
      max-width: 1400px;
      margin: 0 auto;
    }

    .integrations__header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: var(--spacing-6);
      gap: var(--spacing-4);
    }

    .integrations__title {
      font-size: var(--font-size-3xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-gray-900);
      margin: 0 0 var(--spacing-1) 0;
    }

    .integrations__subtitle {
      color: var(--color-gray-600);
      margin: 0;
    }

    .integrations__stats {
      display: flex;
      gap: var(--spacing-4);
    }

    .stat-card {
      background: var(--color-white);
      border: 1px solid var(--color-gray-200);
      border-radius: var(--border-radius-lg);
      padding: var(--spacing-4);
      text-align: center;
      min-width: 80px;
    }

    .stat-value {
      font-size: var(--font-size-2xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-primary-600);
    }

    .stat-label {
      font-size: var(--font-size-sm);
      color: var(--color-gray-600);
      margin-top: var(--spacing-1);
    }

    .integrations__filters {
      margin-bottom: var(--spacing-6);
    }

    .filter-tabs {
      display: flex;
      gap: var(--spacing-2);
      background: var(--color-white);
      border: 1px solid var(--color-gray-200);
      border-radius: var(--border-radius-lg);
      padding: var(--spacing-2);
    }

    .filter-tab {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      padding: var(--spacing-3) var(--spacing-4);
      border: none;
      background: none;
      border-radius: var(--border-radius-md);
      cursor: pointer;
      transition: all 0.2s ease;
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-600);
    }

    .filter-tab:hover {
      background: var(--color-gray-50);
      color: var(--color-gray-900);
    }

    .filter-tab--active {
      background: var(--color-primary-100);
      color: var(--color-primary-700);
    }

    .filter-tab__icon {
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .loading-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: var(--spacing-12);
      text-align: center;
    }

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid var(--color-gray-200);
      border-top: 3px solid var(--color-primary-600);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: var(--spacing-4);
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .integrations-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
      gap: var(--spacing-6);
    }

    .integration-card {
      background: var(--color-white);
      border: 1px solid var(--color-gray-200);
      border-radius: var(--border-radius-lg);
      padding: var(--spacing-6);
      transition: all 0.2s ease;
      position: relative;
    }

    .integration-card:hover {
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .integration-card--connected {
      border-left: 4px solid var(--color-green-500);
    }

    .integration-card--error {
      border-left: 4px solid var(--color-red-500);
    }

    .integration-card--configuring {
      border-left: 4px solid var(--color-yellow-500);
    }

    .integration-card__header {
      display: flex;
      align-items: flex-start;
      gap: var(--spacing-4);
      margin-bottom: var(--spacing-4);
    }

    .integration-card__icon {
      width: 48px;
      height: 48px;
      border-radius: var(--border-radius-lg);
      background: var(--color-gray-100);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
    }

    .integration-card__info {
      flex: 1;
      min-width: 0;
    }

    .integration-card__title {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-900);
      margin: 0 0 var(--spacing-1) 0;
    }

    .integration-card__description {
      color: var(--color-gray-600);
      margin: 0;
      font-size: var(--font-size-sm);
    }

    .integration-card__status {
      flex-shrink: 0;
    }

    .status-badge {
      display: inline-flex;
      align-items: center;
      padding: var(--spacing-1) var(--spacing-2);
      border-radius: var(--border-radius-md);
      font-size: var(--font-size-xs);
      font-weight: var(--font-weight-medium);
    }

    .status-connected {
      background: var(--color-green-100);
      color: var(--color-green-700);
    }

    .status-disconnected {
      background: var(--color-gray-100);
      color: var(--color-gray-700);
    }

    .status-error {
      background: var(--color-red-100);
      color: var(--color-red-700);
    }

    .status-configuring {
      background: var(--color-yellow-100);
      color: var(--color-yellow-700);
    }

    .integration-card__features {
      margin-bottom: var(--spacing-4);
    }

    .features-title {
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-900);
      margin-bottom: var(--spacing-2);
    }

    .features-list {
      display: flex;
      flex-wrap: wrap;
      gap: var(--spacing-2);
    }

    .feature-tag {
      display: inline-flex;
      align-items: center;
      padding: var(--spacing-1) var(--spacing-2);
      background: var(--color-primary-50);
      color: var(--color-primary-700);
      border-radius: var(--border-radius-md);
      font-size: var(--font-size-xs);
      font-weight: var(--font-weight-medium);
    }

    .integration-card__sync {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      margin-bottom: var(--spacing-4);
      font-size: var(--font-size-sm);
      color: var(--color-gray-600);
    }

    .integration-card__actions {
      display: flex;
      gap: var(--spacing-2);
      flex-wrap: wrap;
    }

    .btn-sm {
      padding: var(--spacing-2) var(--spacing-3);
      font-size: var(--font-size-sm);
    }

    .btn-outline {
      background: transparent;
      border: 1px solid var(--color-gray-300);
      color: var(--color-gray-700);
    }

    .btn-outline:hover {
      background: var(--color-gray-50);
      border-color: var(--color-gray-400);
    }

    .btn-danger {
      background: var(--color-red-600);
      color: white;
      border: 1px solid var(--color-red-600);
    }

    .btn-danger:hover {
      background: var(--color-red-700);
      border-color: var(--color-red-700);
    }

    .btn-warning {
      background: var(--color-yellow-500);
      color: white;
      border: 1px solid var(--color-yellow-500);
    }

    .btn-warning:hover {
      background: var(--color-yellow-600);
      border-color: var(--color-yellow-600);
    }

    .btn-spinner {
      width: 16px;
      height: 16px;
      border: 2px solid transparent;
      border-top: 2px solid currentColor;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: var(--spacing-2);
    }

    /* Modal Styles */
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
      padding: var(--spacing-4);
    }

    .modal-content {
      background: var(--color-white);
      border-radius: var(--border-radius-lg);
      width: 100%;
      max-width: 600px;
      max-height: 90vh;
      overflow-y: auto;
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    .modal-content--large {
      max-width: 800px;
    }

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--spacing-6);
      border-bottom: 1px solid var(--color-gray-200);
    }

    .modal-header h3 {
      margin: 0;
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-900);
    }

    .modal-close {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border: none;
      background: none;
      color: var(--color-gray-400);
      cursor: pointer;
      border-radius: var(--border-radius-md);
      transition: all 0.2s ease;
    }

    .modal-close:hover {
      background: var(--color-gray-100);
      color: var(--color-gray-600);
    }

    .modal-body {
      padding: var(--spacing-6);
    }

    .setup-steps {
      margin-bottom: var(--spacing-6);
      padding: var(--spacing-4);
      background: var(--color-blue-50);
      border-radius: var(--border-radius-lg);
      border: 1px solid var(--color-blue-200);
    }

    .setup-steps h4 {
      margin: 0 0 var(--spacing-3) 0;
      color: var(--color-blue-900);
      font-size: var(--font-size-lg);
    }

    .steps-list {
      margin: 0;
      padding-left: var(--spacing-5);
      color: var(--color-blue-800);
    }

    .steps-list li {
      margin-bottom: var(--spacing-2);
    }

    .config-form {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-6);
    }

    .form-section {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-4);
    }

    .form-section h4 {
      margin: 0;
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-900);
      padding-bottom: var(--spacing-2);
      border-bottom: 1px solid var(--color-gray-200);
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-4);
    }

    .form-group {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-2);
    }

    .form-label {
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-700);
    }

    .form-input, .form-select {
      padding: var(--spacing-3);
      border: 1px solid var(--color-gray-300);
      border-radius: var(--border-radius-md);
      font-size: var(--font-size-sm);
      transition: border-color 0.2s ease;
    }

    .form-input:focus, .form-select:focus {
      outline: none;
      border-color: var(--color-primary-500);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .form-help {
      font-size: var(--font-size-xs);
      color: var(--color-gray-500);
    }

    .toggle-switch {
      display: flex;
      align-items: center;
      gap: var(--spacing-3);
    }

    .toggle-input {
      display: none;
    }

    .toggle-label {
      position: relative;
      width: 48px;
      height: 24px;
      background: var(--color-gray-300);
      border-radius: 12px;
      cursor: pointer;
      transition: background-color 0.2s ease;
    }

    .toggle-input:checked + .toggle-label {
      background: var(--color-primary-500);
    }

    .toggle-slider {
      position: absolute;
      top: 2px;
      left: 2px;
      width: 20px;
      height: 20px;
      background: white;
      border-radius: 50%;
      transition: transform 0.2s ease;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .toggle-input:checked + .toggle-label .toggle-slider {
      transform: translateX(24px);
    }

    .toggle-text {
      font-size: var(--font-size-sm);
      color: var(--color-gray-700);
      font-weight: var(--font-weight-medium);
    }

    .modal-actions {
      display: flex;
      justify-content: flex-end;
      gap: var(--spacing-3);
      margin-top: var(--spacing-6);
      padding-top: var(--spacing-4);
      border-top: 1px solid var(--color-gray-200);
    }

    @media (max-width: 768px) {
      .integrations {
        padding: var(--spacing-4);
      }

      .integrations__header {
        flex-direction: column;
        align-items: stretch;
      }

      .integrations__stats {
        justify-content: center;
      }

      .filter-tabs {
        flex-wrap: wrap;
      }

      .integrations-grid {
        grid-template-columns: 1fr;
      }

      .form-row {
        grid-template-columns: 1fr;
      }

      .modal-content--large {
        max-width: 100%;
      }
    }
  `]
})
export class IntegrationsComponent implements OnInit {
  private fb = inject(FormBuilder);

  // Data
  integrations: Integration[] = [];
  filteredIntegrations: Integration[] = [];
  selectedCategory: string = 'all';
  selectedIntegration: Integration | null = null;

  // State
  loading = false;
  saving = false;
  testing = '';
  showConfigModal = false;

  // Form
  configForm!: FormGroup;

  // Categories
  categories = [
    {
      key: 'all',
      label: 'All',
      icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><rect x="3" y="3" width="7" height="7"></rect><rect x="14" y="3" width="7" height="7"></rect><rect x="14" y="14" width="7" height="7"></rect><rect x="3" y="14" width="7" height="7"></rect></svg>'
    },
    {
      key: 'communication',
      label: 'Communication',
      icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg>'
    },
    {
      key: 'project-management',
      label: 'Project Management',
      icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M9 12l2 2 4-4"></path><path d="M21 12c.552 0 1-.448 1-1V5c0-.552-.448-1-1-1H3c-.552 0-1 .448-1 1v6c0 .552.448 1 1 1h9l4-4-4-4H3"></path></svg>'
    },
    {
      key: 'development',
      label: 'Development',
      icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polyline points="16,18 22,12 16,6"></polyline><polyline points="8,6 2,12 8,18"></polyline></svg>'
    },
    {
      key: 'monitoring',
      label: 'Monitoring',
      icon: '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M22 12h-4l-3 9L9 3l-3 9H2"></path></svg>'
    }
  ];

  ngOnInit() {
    this.initializeIntegrations();
    this.loadIntegrations();
  }

  private initializeIntegrations() {
    this.integrations = [
      {
        id: 'slack',
        name: 'slack',
        displayName: 'Slack',
        description: 'Send notifications and updates to Slack channels',
        icon: '<svg width="32" height="32" viewBox="0 0 24 24" fill="#4A154B"><path d="M5.042 15.165a2.528 2.528 0 0 1-2.52 2.523A2.528 2.528 0 0 1 0 15.165a2.527 2.527 0 0 1 2.522-2.52h2.52v2.52zM6.313 15.165a2.527 2.527 0 0 1 2.521-2.52 2.527 2.527 0 0 1 2.521 2.52v6.313A2.528 2.528 0 0 1 8.834 24a2.528 2.528 0 0 1-2.521-2.522v-6.313zM8.834 5.042a2.528 2.528 0 0 1-2.521-2.52A2.528 2.528 0 0 1 8.834 0a2.528 2.528 0 0 1 2.521 2.522v2.52H8.834zM8.834 6.313a2.528 2.528 0 0 1 2.521 2.521 2.528 2.528 0 0 1-2.521 2.521H2.522A2.528 2.528 0 0 1 0 8.834a2.528 2.528 0 0 1 2.522-2.521h6.312zM18.956 8.834a2.528 2.528 0 0 1 2.521-2.521A2.528 2.528 0 0 1 24 8.834a2.528 2.528 0 0 1-2.523 2.521h-2.521V8.834zM17.688 8.834a2.528 2.528 0 0 1-2.523 2.521 2.527 2.527 0 0 1-2.52-2.521V2.522A2.527 2.527 0 0 1 15.165 0a2.528 2.528 0 0 1 2.523 2.522v6.312zM15.165 18.956a2.528 2.528 0 0 1 2.523 2.521A2.528 2.528 0 0 1 15.165 24a2.527 2.527 0 0 1-2.52-2.523v-2.521h2.52zM15.165 17.688a2.527 2.527 0 0 1-2.52-2.523 2.526 2.526 0 0 1 2.52-2.52h6.313A2.527 2.527 0 0 1 24 15.165a2.528 2.528 0 0 1-2.522 2.523h-6.313z"/></svg>',
        category: 'communication',
        status: 'disconnected',
        isEnabled: false,
        config: {},
        features: ['Real-time notifications', 'Bug alerts', 'Project updates', 'Custom channels'],
        setupSteps: [
          'Create a Slack app in your workspace',
          'Enable Incoming Webhooks',
          'Create a webhook for your desired channel',
          'Copy the webhook URL and paste it below',
          'Test the connection and save'
        ]
      },
      {
        id: 'teams',
        name: 'teams',
        displayName: 'Microsoft Teams',
        description: 'Send notifications and updates to Microsoft Teams channels',
        icon: '<svg width="32" height="32" viewBox="0 0 24 24" fill="#6264A7"><path d="M21.53 4.306c.63.406 1.045 1.115 1.045 1.925v11.538c0 .81-.415 1.519-1.045 1.925L12.53 24.306c-.63.406-1.43.406-2.06 0L1.47 19.694C.84 19.288.425 18.579.425 17.769V6.231c0-.81.415-1.519 1.045-1.925L10.47.694c.63-.406 1.43-.406 2.06 0l9 5.612z"/></svg>',
        category: 'communication',
        status: 'connected',
        isEnabled: true,
        config: { webhookUrl: 'https://outlook.office.com/webhook/...' },
        lastSync: new Date(Date.now() - 2 * 60 * 60 * 1000),
        features: ['Team notifications', 'Bug reports', 'Status updates', 'Adaptive cards'],
        setupSteps: [
          'Go to your Teams channel',
          'Click on "..." and select "Connectors"',
          'Find "Incoming Webhook" and configure it',
          'Copy the webhook URL',
          'Paste the URL below and test the connection'
        ]
      },
      {
        id: 'jira',
        name: 'jira',
        displayName: 'JIRA',
        description: 'Synchronize bugs and issues with Atlassian JIRA',
        icon: '<svg width="32" height="32" viewBox="0 0 24 24" fill="#0052CC"><path d="M11.53 2c0 2.4 1.97 4.35 4.35 4.35h1.78v1.7c0 2.4 1.97 4.35 4.35 4.35V2.7c0-.39-.31-.7-.7-.7H11.53zM6.77 6.8c0 2.4 1.97 4.35 4.35 4.35h1.78v1.7c0 2.4 1.97 4.35 4.35 4.35V7.5c0-.39-.31-.7-.7-.7H6.77zM2 11.6c0 2.4 1.97 4.35 4.35 4.35h1.78v1.7c0 2.4 1.97 4.35 4.35 4.35V12.3c0-.39-.31-.7-.7-.7H2z"/></svg>',
        category: 'project-management',
        status: 'error',
        isEnabled: false,
        config: {},
        features: ['Two-way sync', 'Issue tracking', 'Status mapping', 'Comment sync'],
        setupSteps: [
          'Create an API token in your JIRA account',
          'Note your JIRA instance URL and project key',
          'Enter your credentials below',
          'Configure field mappings',
          'Test the connection and enable sync'
        ]
      },
      {
        id: 'github',
        name: 'github',
        displayName: 'GitHub',
        description: 'Link bugs to GitHub issues and pull requests',
        icon: '<svg width="32" height="32" viewBox="0 0 24 24" fill="#181717"><path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 *********** 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/></svg>',
        category: 'development',
        status: 'disconnected',
        isEnabled: false,
        config: {},
        features: ['Issue linking', 'PR integration', 'Commit tracking', 'Release notes'],
        setupSteps: [
          'Generate a Personal Access Token in GitHub',
          'Grant "repo" and "issues" permissions',
          'Enter your repository information',
          'Test the connection',
          'Configure webhook for real-time updates'
        ]
      }
    ];

    this.filterIntegrations();
  }

  private loadIntegrations() {
    this.loading = true;

    // Simulate API call
    setTimeout(() => {
      this.loading = false;
    }, 800);
  }

  private filterIntegrations() {
    if (this.selectedCategory === 'all') {
      this.filteredIntegrations = [...this.integrations];
    } else {
      this.filteredIntegrations = this.integrations.filter(
        integration => integration.category === this.selectedCategory
      );
    }
  }

  selectCategory(category: string) {
    this.selectedCategory = category;
    this.filterIntegrations();
  }

  getConnectedCount(): number {
    return this.integrations.filter(integration => integration.status === 'connected').length;
  }

  getStatusLabel(status: string): string {
    const labels: Record<string, string> = {
      connected: 'Connected',
      disconnected: 'Not Connected',
      error: 'Error',
      configuring: 'Configuring'
    };
    return labels[status] || status;
  }

  configureIntegration(integration: Integration) {
    this.selectedIntegration = integration;
    this.buildConfigForm();
    this.showConfigModal = true;
  }

  private buildConfigForm() {
    if (!this.selectedIntegration) return;

    const formConfig: any = {
      enabled: [this.selectedIntegration.isEnabled]
    };

    switch (this.selectedIntegration.name) {
      case 'slack':
        formConfig.webhookUrl = [this.selectedIntegration.config.webhookUrl || '', Validators.required];
        formConfig.channel = [this.selectedIntegration.config.channel || ''];
        break;
      case 'teams':
        formConfig.webhookUrl = [this.selectedIntegration.config.webhookUrl || '', Validators.required];
        break;
      case 'jira':
        formConfig.baseUrl = [this.selectedIntegration.config.baseUrl || '', Validators.required];
        formConfig.projectKey = [this.selectedIntegration.config.projectKey || '', Validators.required];
        formConfig.username = [this.selectedIntegration.config.username || '', Validators.required];
        formConfig.apiToken = [this.selectedIntegration.config.apiToken || '', Validators.required];
        break;
      case 'github':
        formConfig.accessToken = [this.selectedIntegration.config.accessToken || '', Validators.required];
        formConfig.repository = [this.selectedIntegration.config.repository || '', Validators.required];
        break;
    }

    this.configForm = this.fb.group(formConfig);
  }

  saveConfiguration() {
    if (!this.configForm || this.configForm.invalid || !this.selectedIntegration) return;

    this.saving = true;
    const formValue = this.configForm.value;

    // Simulate API call
    setTimeout(() => {
      // Update the integration
      this.selectedIntegration!.config = { ...formValue };
      this.selectedIntegration!.isEnabled = formValue.enabled;
      this.selectedIntegration!.status = formValue.enabled ? 'connected' : 'disconnected';

      if (formValue.enabled) {
        this.selectedIntegration!.lastSync = new Date();
      }

      // Update in the main array
      const index = this.integrations.findIndex(i => i.id === this.selectedIntegration!.id);
      if (index !== -1) {
        this.integrations[index] = { ...this.selectedIntegration! };
      }

      this.saving = false;
      this.closeConfigModal();
    }, 1000);
  }

  testConnection(integration: Integration) {
    this.testing = integration.id;

    // Simulate API call
    setTimeout(() => {
      // Simulate success/failure
      const success = Math.random() > 0.3;

      if (success) {
        integration.status = 'connected';
        integration.lastSync = new Date();
        alert('Connection test successful!');
      } else {
        integration.status = 'error';
        alert('Connection test failed. Please check your configuration.');
      }

      this.testing = '';
    }, 2000);
  }

  disconnectIntegration(integration: Integration) {
    if (!confirm(`Are you sure you want to disconnect ${integration.displayName}?`)) {
      return;
    }

    integration.status = 'disconnected';
    integration.isEnabled = false;
    integration.config = {};
    integration.lastSync = undefined;
  }

  closeConfigModal() {
    this.showConfigModal = false;
    this.selectedIntegration = null;
    this.configForm = this.fb.group({});
  }

  // Utility methods
  trackByIntegration(index: number, integration: Integration): string {
    return integration.id;
  }
}
