import { Routes } from '@angular/router';
import { AuthGuard, AdminGuard, GuestGuard } from './core/guards/auth.guard';
import { UserRole } from './core/models/user.model';

export const routes: Routes = [
  // Redirect root to dashboard
  {
    path: '',
    redirectTo: '/dashboard',
    pathMatch: 'full'
  },

  // Authentication routes (accessible only when not logged in)
  {
    path: 'auth',
    canActivate: [GuestGuard],
    children: [
      {
        path: 'login',
        loadComponent: () => import('./features/auth/login/login.component').then(m => m.LoginComponent)
      },
      {
        path: '',
        redirectTo: 'login',
        pathMatch: 'full'
      }
    ]
  },

  // Main application routes (require authentication)
  {
    path: '',
    canActivate: [AuthGuard],
    loadComponent: () => import('./layout/components/main-layout/main-layout.component').then(m => m.MainLayoutComponent),
    children: [
      // Dashboard
      {
        path: 'dashboard',
        loadComponent: () => import('./features/dashboard/dashboard.component').then(m => m.DashboardComponent)
      },

      // Projects
      {
        path: 'projects',
        children: [
          {
            path: '',
            loadComponent: () => import('./features/projects/project-list/project-list.component').then(m => m.ProjectListComponent)
          },
          {
            path: 'create',
            loadComponent: () => import('./features/projects/project-form/project-form.component').then(m => m.ProjectFormComponent),
            data: {
              roles: [UserRole.ADMIN, UserRole.PROJECT_MANAGER],
              permissions: ['projects.create']
            }
          },
          {
            path: ':id',
            loadComponent: () => import('./features/projects/project-detail/project-detail.component').then(m => m.ProjectDetailComponent)
          },
          {
            path: ':id/edit',
            loadComponent: () => import('./features/projects/project-form/project-form.component').then(m => m.ProjectFormComponent),
            data: {
              roles: [UserRole.ADMIN, UserRole.PROJECT_MANAGER],
              permissions: ['projects.update']
            }
          }
        ]
      },

      // Bugs
      {
        path: 'bugs',
        children: [
          {
            path: '',
            loadComponent: () => import('./features/bugs/bug-list/bug-list.component').then(m => m.BugListComponent)
          },
          {
            path: 'create',
            loadComponent: () => import('./features/bugs/bug-form/bug-form.component').then(m => m.BugFormComponent),
            data: { permissions: ['bugs.create'] }
          },
          {
            path: 'my',
            loadComponent: () => import('./features/bugs/bug-list/bug-list.component').then(m => m.BugListComponent)
          },
          {
            path: 'assigned',
            loadComponent: () => import('./features/bugs/bug-list/bug-list.component').then(m => m.BugListComponent)
          },
          {
            path: ':id',
            loadComponent: () => import('./features/bugs/bug-detail/bug-detail.component').then(m => m.BugDetailComponent)
          },
          {
            path: ':id/edit',
            loadComponent: () => import('./features/bugs/bug-form/bug-form.component').then(m => m.BugFormComponent),
            data: { permissions: ['bugs.update'] }
          }
        ]
      },

      // Reports
      {
        path: 'reports',
        data: { permissions: ['reports.read'] },
        children: [
          {
            path: '',
            redirectTo: 'bugs',
            pathMatch: 'full'
          },
          {
            path: 'bugs',
            loadComponent: () => import('./features/reports/bug-reports/bug-reports.component').then(m => m.BugReportsComponent)
          },
          {
            path: 'projects',
            loadComponent: () => import('./features/reports/project-reports/project-reports.component').then(m => m.ProjectReportsComponent)
          },
          {
            path: 'team',
            loadComponent: () => import('./features/reports/team-reports/team-reports.component').then(m => m.TeamReportsComponent),
            data: {
              roles: [UserRole.ADMIN, UserRole.PROJECT_MANAGER],
              permissions: ['reports.team']
            }
          },
          {
            path: 'custom',
            loadComponent: () => import('./features/reports/custom-reports/custom-reports.component').then(m => m.CustomReportsComponent),
            data: {
              roles: [UserRole.ADMIN],
              permissions: ['reports.custom']
            }
          }
        ]
      },

      // Admin routes (admin only)
      {
        path: 'admin',
        canActivate: [AdminGuard],
        children: [
          {
            path: '',
            redirectTo: 'users',
            pathMatch: 'full'
          },
          {
            path: 'users',
            loadComponent: () => import('./features/admin/user-management/user-management.component').then(m => m.UserManagementComponent)
          },
          {
            path: 'roles',
            loadComponent: () => import('./features/admin/role-management/role-management.component').then(m => m.RoleManagementComponent)
          },
          {
            path: 'settings',
            loadComponent: () => import('./features/admin/system-settings/system-settings.component').then(m => m.SystemSettingsComponent)
          },
          {
            path: 'integrations',
            loadComponent: () => import('./features/admin/integrations/integrations.component').then(m => m.IntegrationsComponent)
          }
        ]
      },

      // User profile and settings
      {
        path: 'profile',
        loadComponent: () => import('./features/profile/profile.component').then(m => m.ProfileComponent)
      },
      {
        path: 'settings',
        loadComponent: () => import('./features/settings/settings.component').then(m => m.SettingsComponent)
      }
    ]
  },

  // Error pages
  {
    path: 'unauthorized',
    loadComponent: () => import('./shared/components/unauthorized/unauthorized.component').then(m => m.UnauthorizedComponent)
  },
  {
    path: 'not-found',
    loadComponent: () => import('./shared/components/not-found/not-found.component').then(m => m.NotFoundComponent)
  },

  // Wildcard route - must be last
  {
    path: '**',
    redirectTo: '/not-found'
  }
];
