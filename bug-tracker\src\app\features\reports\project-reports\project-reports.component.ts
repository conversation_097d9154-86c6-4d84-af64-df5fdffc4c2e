import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { ProjectService } from '../../../core/services/project.service';
import { BugService } from '../../../core/services/bug.service';
import { UserService } from '../../../core/services/user.service';
import { Project, ProjectStatus } from '../../../core/models/project.model';
import { User, UserRole } from '../../../core/models/user.model';
import { BugStatus, BugSeverity } from '../../../core/models/bug.model';

interface ProjectReportData {
  totalProjects: number;
  activeProjects: number;
  completedProjects: number;
  onHoldProjects: number;
  totalTeamMembers: number;
  averageProjectDuration: number;
  projectHealthScores: { projectName: string; healthScore: number; status: string }[];
  bugsByProject: { projectName: string; totalBugs: number; openBugs: number; closedBugs: number }[];
  teamProductivity: { memberName: string; projectsAssigned: number; bugsResolved: number; efficiency: number }[];
  projectTimelines: { projectName: string; startDate: string; endDate: string; progress: number }[];
  statusDistribution: { [key: string]: number };
  monthlyProgress: { month: string; projectsStarted: number; projectsCompleted: number }[];
}

@Component({
  selector: 'app-project-reports',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  template: `
    <div class="project-reports-container">
      <div class="reports-header">
        <h1 class="page-title">Project Reports & Analytics</h1>
        <p class="page-subtitle">Comprehensive project health, team productivity, and timeline analysis</p>
      </div>

      <!-- Filters -->
      <div class="filters-section">
        <form [formGroup]="filtersForm" class="filters-form">
          <div class="filter-group">
            <label class="filter-label">Date Range</label>
            <div class="date-range">
              <input type="date" formControlName="startDate" class="form-control">
              <span class="date-separator">to</span>
              <input type="date" formControlName="endDate" class="form-control">
            </div>
          </div>

          <div class="filter-group">
            <label class="filter-label">Project Status</label>
            <select formControlName="status" class="form-control">
              <option value="">All Statuses</option>
              <option value="PLANNING">Planning</option>
              <option value="ACTIVE">Active</option>
              <option value="ON_HOLD">On Hold</option>
              <option value="COMPLETED">Completed</option>
              <option value="ARCHIVED">Archived</option>
            </select>
          </div>

          <div class="filter-group">
            <label class="filter-label">Project Manager</label>
            <select formControlName="managerId" class="form-control">
              <option value="">All Managers</option>
              <option *ngFor="let manager of projectManagers" [value]="manager.id">
                {{ manager.firstName }} {{ manager.lastName }}
              </option>
            </select>
          </div>

          <div class="filter-actions">
            <button type="button" (click)="applyFilters()" class="btn btn-primary">
              Apply Filters
            </button>
            <button type="button" (click)="resetFilters()" class="btn btn-outline">
              Reset
            </button>
            <button type="button" (click)="exportReport()" class="btn btn-success">
              Export Report
            </button>
          </div>
        </form>
      </div>

      <!-- Loading State -->
      <div *ngIf="loading" class="loading-container">
        <div class="spinner"></div>
        <p>Loading project analytics...</p>
      </div>

      <!-- Report Content -->
      <div *ngIf="!loading && reportData" class="report-content">
        <!-- Summary Cards -->
        <div class="summary-cards">
          <div class="summary-card">
            <div class="card-icon total">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M3 7V17C3 18.1 3.9 19 5 19H19C20.1 19 21 18.1 21 17V7C21 5.9 20.1 5 19 5H5C3.9 5 3 5.9 3 7Z"></path>
                <path d="M8 9H16V11H8V9Z" fill="white"></path>
                <path d="M8 13H13V15H8V13Z" fill="white"></path>
              </svg>
            </div>
            <div class="card-content">
              <h3>{{ reportData.totalProjects }}</h3>
              <p>Total Projects</p>
            </div>
          </div>

          <div class="summary-card">
            <div class="card-icon active">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M8 12L11 15L16 9" stroke="white" stroke-width="2" fill="none"></path>
              </svg>
            </div>
            <div class="card-content">
              <h3>{{ reportData.activeProjects }}</h3>
              <p>Active Projects</p>
            </div>
          </div>

          <div class="summary-card">
            <div class="card-icon completed">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"></path>
              </svg>
            </div>
            <div class="card-content">
              <h3>{{ reportData.completedProjects }}</h3>
              <p>Completed Projects</p>
            </div>
          </div>

          <div class="summary-card">
            <div class="card-icon team">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z"></path>
                <path d="M12 14C8.13401 14 5 17.134 5 21H19C19 17.134 15.866 14 12 14Z"></path>
              </svg>
            </div>
            <div class="card-content">
              <h3>{{ reportData.totalTeamMembers }}</h3>
              <p>Team Members</p>
            </div>
          </div>

          <div class="summary-card">
            <div class="card-icon duration">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M12 6V12L16 14" stroke="white" stroke-width="2" fill="none"></path>
              </svg>
            </div>
            <div class="card-content">
              <h3>{{ reportData.averageProjectDuration }}d</h3>
              <p>Avg Duration</p>
            </div>
          </div>
        </div>

        <!-- Charts and Analytics Section -->
        <div class="analytics-section">
          <!-- Project Health Overview -->
          <div class="chart-card full-width">
            <div class="chart-header">
              <h3>Project Health Overview</h3>
              <p>Health scores and status for all projects</p>
            </div>
            <div class="project-health-grid">
              <div *ngFor="let project of reportData.projectHealthScores" class="health-card">
                <div class="health-header">
                  <h4>{{ project.projectName }}</h4>
                  <span class="status-badge" [class]="'status-' + project.status.toLowerCase()">
                    {{ project.status }}
                  </span>
                </div>
                <div class="health-score">
                  <div class="score-circle" [style.background]="getHealthColor(project.healthScore)">
                    <span>{{ project.healthScore }}%</span>
                  </div>
                  <div class="score-label">Health Score</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Project Status Distribution -->
          <div class="chart-card">
            <div class="chart-header">
              <h3>Project Status Distribution</h3>
              <p>Current distribution of projects by status</p>
            </div>
            <div class="chart-placeholder">
              <p>Chart visualization will be implemented in the next phase</p>
            </div>
          </div>

          <!-- Monthly Progress -->
          <div class="chart-card">
            <div class="chart-header">
              <h3>Monthly Project Progress</h3>
              <p>Projects started vs completed over time</p>
            </div>
            <div class="chart-placeholder">
              <p>Chart visualization will be implemented in the next phase</p>
            </div>
          </div>
        </div>

        <!-- Bugs by Project -->
        <div class="chart-card full-width">
          <div class="chart-header">
            <h3>Bug Analysis by Project</h3>
            <p>Bug counts and resolution status for each project</p>
          </div>
          <div class="chart-placeholder">
            <p>Chart visualization will be implemented in the next phase</p>
          </div>
        </div>

        <!-- Team Productivity -->
        <div class="chart-card full-width">
          <div class="chart-header">
            <h3>Team Productivity Analysis</h3>
            <p>Individual team member performance and efficiency metrics</p>
          </div>
          <div class="productivity-grid">
            <div *ngFor="let member of reportData.teamProductivity" class="productivity-card">
              <div class="member-info">
                <h4>{{ member.memberName }}</h4>
                <div class="member-stats">
                  <div class="stat">
                    <span class="stat-value">{{ member.projectsAssigned }}</span>
                    <span class="stat-label">Projects</span>
                  </div>
                  <div class="stat">
                    <span class="stat-value">{{ member.bugsResolved }}</span>
                    <span class="stat-label">Bugs Resolved</span>
                  </div>
                  <div class="stat">
                    <span class="stat-value">{{ member.efficiency }}%</span>
                    <span class="stat-label">Efficiency</span>
                  </div>
                </div>
              </div>
              <div class="efficiency-bar">
                <div class="efficiency-fill" [style.width.%]="member.efficiency"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- Project Timelines -->
        <div class="chart-card full-width">
          <div class="chart-header">
            <h3>Project Timeline Overview</h3>
            <p>Project schedules and progress tracking</p>
          </div>
          <div class="timeline-container">
            <div *ngFor="let timeline of reportData.projectTimelines" class="timeline-item">
              <div class="timeline-header">
                <h4>{{ timeline.projectName }}</h4>
                <span class="timeline-dates">
                  {{ formatDate(timeline.startDate) }} - {{ formatDate(timeline.endDate) }}
                </span>
              </div>
              <div class="progress-container">
                <div class="progress-bar">
                  <div class="progress-fill" [style.width.%]="timeline.progress"></div>
                </div>
                <span class="progress-text">{{ timeline.progress }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Error State -->
      <div *ngIf="errorMessage" class="error-container">
        <div class="error-card">
          <h3>Error Loading Project Reports</h3>
          <p>{{ errorMessage }}</p>
          <button (click)="loadReportData()" class="btn btn-primary">Retry</button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .project-reports-container {
      padding: 2rem;
      max-width: 1400px;
      margin: 0 auto;
    }

    .reports-header {
      margin-bottom: 2rem;
    }

    .page-title {
      font-size: 2rem;
      font-weight: 700;
      color: var(--color-text-primary);
      margin-bottom: 0.5rem;
    }

    .page-subtitle {
      color: var(--color-text-secondary);
      font-size: 1.1rem;
    }

    .filters-section {
      background: white;
      border-radius: 12px;
      padding: 1.5rem;
      margin-bottom: 2rem;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .filters-form {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      align-items: end;
    }

    .filter-group {
      display: flex;
      flex-direction: column;
    }

    .filter-label {
      font-weight: 600;
      color: var(--color-text-primary);
      margin-bottom: 0.5rem;
      font-size: 0.9rem;
    }

    .date-range {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .date-separator {
      color: var(--color-text-secondary);
      font-size: 0.9rem;
    }

    .filter-actions {
      display: flex;
      gap: 0.5rem;
      flex-wrap: wrap;
    }

    .summary-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
      gap: 1.5rem;
      margin-bottom: 2rem;
    }

    .summary-card {
      background: white;
      border-radius: 12px;
      padding: 1.5rem;
      display: flex;
      align-items: center;
      gap: 1rem;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: transform 0.2s ease;
    }

    .summary-card:hover {
      transform: translateY(-2px);
    }

    .card-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
    }

    .card-icon.total { background: var(--color-primary); }
    .card-icon.active { background: var(--color-success); }
    .card-icon.completed { background: var(--color-info); }
    .card-icon.team { background: var(--color-warning); }
    .card-icon.duration { background: var(--color-secondary); }

    .card-content h3 {
      font-size: 2rem;
      font-weight: 700;
      color: var(--color-text-primary);
      margin: 0;
    }

    .card-content p {
      color: var(--color-text-secondary);
      margin: 0;
      font-size: 0.9rem;
    }

    .analytics-section {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 2rem;
    }

    .chart-card {
      background: white;
      border-radius: 12px;
      padding: 1.5rem;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .chart-card.full-width {
      grid-column: 1 / -1;
    }

    .chart-header {
      margin-bottom: 1.5rem;
    }

    .chart-header h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--color-text-primary);
      margin-bottom: 0.25rem;
    }

    .chart-header p {
      color: var(--color-text-secondary);
      font-size: 0.9rem;
      margin: 0;
    }

    .chart-container {
      position: relative;
      height: 300px;
    }

    .chart-placeholder {
      height: 300px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f9fafb;
      border-radius: 8px;
      color: #6b7280;
      font-style: italic;
    }

    .project-health-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 1rem;
    }

    .health-card {
      background: #f8fafc;
      border-radius: 8px;
      padding: 1rem;
      border: 1px solid #e2e8f0;
    }

    .health-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
    }

    .health-header h4 {
      margin: 0;
      font-size: 1rem;
      font-weight: 600;
      color: var(--color-text-primary);
    }

    .status-badge {
      padding: 0.25rem 0.75rem;
      border-radius: 20px;
      font-size: 0.75rem;
      font-weight: 600;
      text-transform: uppercase;
    }

    .status-active { background: #dcfce7; color: #166534; }
    .status-planning { background: #fef3c7; color: #92400e; }
    .status-completed { background: #dbeafe; color: #1e40af; }
    .status-on_hold { background: #fecaca; color: #991b1b; }

    .health-score {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .score-circle {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 700;
      font-size: 0.9rem;
    }

    .score-label {
      font-size: 0.9rem;
      color: var(--color-text-secondary);
    }

    .productivity-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1rem;
    }

    .productivity-card {
      background: #f8fafc;
      border-radius: 8px;
      padding: 1rem;
      border: 1px solid #e2e8f0;
    }

    .member-info h4 {
      margin: 0 0 1rem 0;
      font-size: 1rem;
      font-weight: 600;
      color: var(--color-text-primary);
    }

    .member-stats {
      display: flex;
      gap: 1rem;
      margin-bottom: 1rem;
    }

    .stat {
      text-align: center;
    }

    .stat-value {
      display: block;
      font-size: 1.25rem;
      font-weight: 700;
      color: var(--color-primary);
    }

    .stat-label {
      font-size: 0.75rem;
      color: var(--color-text-secondary);
    }

    .efficiency-bar {
      height: 8px;
      background: #e2e8f0;
      border-radius: 4px;
      overflow: hidden;
    }

    .efficiency-fill {
      height: 100%;
      background: linear-gradient(90deg, #10b981, #059669);
      transition: width 0.3s ease;
    }

    .timeline-container {
      space-y: 1rem;
    }

    .timeline-item {
      background: #f8fafc;
      border-radius: 8px;
      padding: 1rem;
      border: 1px solid #e2e8f0;
      margin-bottom: 1rem;
    }

    .timeline-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
    }

    .timeline-header h4 {
      margin: 0;
      font-size: 1rem;
      font-weight: 600;
      color: var(--color-text-primary);
    }

    .timeline-dates {
      font-size: 0.9rem;
      color: var(--color-text-secondary);
    }

    .progress-container {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .progress-bar {
      flex: 1;
      height: 8px;
      background: #e2e8f0;
      border-radius: 4px;
      overflow: hidden;
    }

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, var(--color-primary), var(--color-primary-dark));
      transition: width 0.3s ease;
    }

    .progress-text {
      font-size: 0.9rem;
      font-weight: 600;
      color: var(--color-primary);
      min-width: 40px;
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 4rem;
      color: var(--color-text-secondary);
    }

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid var(--color-border);
      border-top: 4px solid var(--color-primary);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 1rem;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .error-container {
      display: flex;
      justify-content: center;
      padding: 2rem;
    }

    .error-card {
      background: white;
      border-radius: 12px;
      padding: 2rem;
      text-align: center;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      max-width: 400px;
    }

    .error-card h3 {
      color: var(--color-danger);
      margin-bottom: 1rem;
    }

    .error-card p {
      color: var(--color-text-secondary);
      margin-bottom: 1.5rem;
    }

    @media (max-width: 768px) {
      .project-reports-container {
        padding: 1rem;
      }

      .filters-form {
        grid-template-columns: 1fr;
      }

      .summary-cards {
        grid-template-columns: 1fr;
      }

      .analytics-section {
        grid-template-columns: 1fr;
      }

      .project-health-grid {
        grid-template-columns: 1fr;
      }

      .productivity-grid {
        grid-template-columns: 1fr;
      }

      .timeline-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
      }

      .member-stats {
        justify-content: space-between;
      }
    }
  `]
})
export class ProjectReportsComponent implements OnInit {

  private fb = inject(FormBuilder);
  private projectService = inject(ProjectService);
  private bugService = inject(BugService);
  private userService = inject(UserService);

  filtersForm: FormGroup;
  loading = false;
  errorMessage = '';
  reportData: ProjectReportData | null = null;
  projects: Project[] = [];
  projectManagers: User[] = [];



  constructor() {
    this.filtersForm = this.fb.group({
      startDate: [''],
      endDate: [''],
      status: [''],
      managerId: ['']
    });
  }

  ngOnInit() {
    this.initializeDateRange();
    this.loadProjects();
    this.loadProjectManagers();
    this.loadReportData();
  }



  private initializeDateRange() {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - 12); // Last 12 months

    this.filtersForm.patchValue({
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0]
    });
  }

  private loadProjects() {
    this.projects = this.projectService.getAllProjects();
  }

  private loadProjectManagers() {
    this.userService.getAllUsers().subscribe({
      next: (users) => {
        this.projectManagers = users.filter(user =>
          user.role === UserRole.PROJECT_MANAGER || user.role === UserRole.ADMIN
        );
      },
      error: (error) => {
        console.error('Error loading project managers:', error);
      }
    });
  }

  loadReportData() {
    this.loading = true;
    this.errorMessage = '';

    // Simulate loading project analytics data
    setTimeout(() => {
      this.reportData = this.generateProjectReportData();
      this.loading = false;

      // Initialize charts after data is loaded
      setTimeout(() => {
        this.initializeCharts();
      }, 100);
    }, 1000);
  }

  private generateProjectReportData(): ProjectReportData {
    const activeProjects = this.projects.filter(p => p.status === ProjectStatus.ACTIVE).length;
    const completedProjects = this.projects.filter(p => p.status === ProjectStatus.COMPLETED).length;
    const onHoldProjects = this.projects.filter(p => p.status === ProjectStatus.ON_HOLD).length;

    return {
      totalProjects: this.projects.length,
      activeProjects: activeProjects,
      completedProjects: completedProjects,
      onHoldProjects: onHoldProjects,
      totalTeamMembers: this.projectManagers.length + 15, // Mock additional team members
      averageProjectDuration: Math.floor(Math.random() * 90) + 30, // 30-120 days
      projectHealthScores: this.generateProjectHealthScores(),
      bugsByProject: this.generateBugsByProject(),
      teamProductivity: this.generateTeamProductivity(),
      projectTimelines: this.generateProjectTimelines(),
      statusDistribution: this.generateStatusDistribution(),
      monthlyProgress: this.generateMonthlyProgress()
    };
  }

  private generateProjectHealthScores() {
    return this.projects.slice(0, 6).map(project => ({
      projectName: project.name,
      healthScore: Math.floor(Math.random() * 40) + 60, // 60-100%
      status: project.status
    }));
  }

  private generateBugsByProject() {
    return this.projects.slice(0, 5).map(project => {
      const totalBugs = Math.floor(Math.random() * 50) + 10;
      const openBugs = Math.floor(totalBugs * 0.3);
      return {
        projectName: project.name,
        totalBugs: totalBugs,
        openBugs: openBugs,
        closedBugs: totalBugs - openBugs
      };
    });
  }

  private generateTeamProductivity() {
    return this.projectManagers.slice(0, 6).map(manager => ({
      memberName: `${manager.firstName} ${manager.lastName}`,
      projectsAssigned: Math.floor(Math.random() * 5) + 1,
      bugsResolved: Math.floor(Math.random() * 30) + 5,
      efficiency: Math.floor(Math.random() * 30) + 70 // 70-100%
    }));
  }

  private generateProjectTimelines() {
    return this.projects.slice(0, 5).map(project => {
      const startDate = new Date();
      startDate.setMonth(startDate.getMonth() - Math.floor(Math.random() * 6));
      const endDate = new Date(startDate);
      endDate.setMonth(endDate.getMonth() + Math.floor(Math.random() * 6) + 3);

      return {
        projectName: project.name,
        startDate: startDate.toISOString().split('T')[0],
        endDate: endDate.toISOString().split('T')[0],
        progress: Math.floor(Math.random() * 80) + 20 // 20-100%
      };
    });
  }

  private generateStatusDistribution() {
    return {
      'Active': this.projects.filter(p => p.status === ProjectStatus.ACTIVE).length,
      'Planning': this.projects.filter(p => p.status === ProjectStatus.PLANNING).length,
      'Completed': this.projects.filter(p => p.status === ProjectStatus.COMPLETED).length,
      'On Hold': this.projects.filter(p => p.status === ProjectStatus.ON_HOLD).length,
      'Archived': this.projects.filter(p => p.status === ProjectStatus.ARCHIVED).length
    };
  }

  private generateMonthlyProgress() {
    const months = [];
    const currentDate = new Date();

    for (let i = 5; i >= 0; i--) {
      const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
      const monthName = date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });

      months.push({
        month: monthName,
        projectsStarted: Math.floor(Math.random() * 8) + 2,
        projectsCompleted: Math.floor(Math.random() * 5) + 1
      });
    }

    return months;
  }

  applyFilters() {
    this.loadReportData();
  }

  resetFilters() {
    this.filtersForm.reset();
    this.initializeDateRange();
    this.loadReportData();
  }

  exportReport() {
    // TODO: Implement export functionality
    console.log('Export project report functionality to be implemented');
    alert('Export functionality will be implemented in the next phase');
  }

  getHealthColor(score: number): string {
    if (score >= 80) return '#10b981'; // Green
    if (score >= 60) return '#f59e0b'; // Yellow
    return '#ef4444'; // Red
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  }










}
