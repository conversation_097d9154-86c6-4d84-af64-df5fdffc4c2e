import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-not-found',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="error-page">
      <div class="error-content">
        <div class="error-icon">
          <svg width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="10"></circle>
            <path d="M9,9h6v6h-6z"></path>
            <path d="M9 1v6h6V1"></path>
          </svg>
        </div>
        <h1>404 - Page Not Found</h1>
        <p>The page you're looking for doesn't exist.</p>
        <a routerLink="/dashboard" class="btn btn-primary">Go to Dashboard</a>
      </div>
    </div>
  `,
  styles: [`
    .error-page {
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--color-gray-50);
      padding: var(--spacing-4);
    }

    .error-content {
      text-align: center;
      max-width: 400px;
    }

    .error-icon {
      color: var(--color-warning-500);
      margin-bottom: var(--spacing-6);
    }

    h1 {
      font-size: var(--font-size-3xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-gray-900);
      margin-bottom: var(--spacing-4);
    }

    p {
      color: var(--color-gray-600);
      margin-bottom: var(--spacing-6);
    }
  `]
})
export class NotFoundComponent {
}
