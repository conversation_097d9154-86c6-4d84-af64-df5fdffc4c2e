.sidebar {
  width: 100%;
  height: 100%;
  background-color: var(--color-white);
  border-right: 1px solid var(--color-gray-200);
  display: flex;
  flex-direction: column;
  overflow: hidden;

  &--collapsed {
    .sidebar__nav-link-wrapper {
      margin: 0 var(--spacing-2);
    }

    .sidebar__nav-text,
    .sidebar__nav-chevron,
    .sidebar__nav-badge {
      display: none !important;
    }

    .sidebar__nav-sublist {
      display: none !important;
    }

    .sidebar__nav-link {
      justify-content: flex-start !important;
      padding: var(--spacing-3) var(--spacing-4) !important;
    }
  }

  &__content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  &__nav {
    flex: 1;
    padding: var(--spacing-4) 0;
    overflow-y: auto;
    overflow-x: hidden;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--color-gray-300);
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: var(--color-gray-400);
    }
  }

  &__nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  &__nav-item {
    margin-bottom: var(--spacing-1);

    &--hidden {
      display: none;
    }
  }

  &__nav-link-wrapper {
    position: relative;
    margin: 0 var(--spacing-3);
  }

  &__nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3) var(--spacing-4);
    color: var(--color-gray-600);
    text-decoration: none;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-fast);
    border: none;
    background: none;
    width: 100%;
    cursor: pointer;
    position: relative;
    border-radius: var(--border-radius-lg);
    min-height: 44px;

    &:hover {
      background-color: var(--color-gray-50);
      color: var(--color-gray-900);
    }

    &--active {
      background-color: var(--color-primary-600);
      color: var(--color-white);
      font-weight: var(--font-weight-semibold);

      .sidebar__nav-icon {
        color: var(--color-white);
      }
    }

    &--expandable {
      justify-content: space-between;
    }

    &--expanded {
      background-color: var(--color-gray-50);
      color: var(--color-gray-900);

      .sidebar__nav-chevron {
        transform: rotate(90deg);
      }
    }
  }

  &__nav-icon {
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: inherit;

    svg {
      width: 20px;
      height: 20px;
      display: block;
    }
  }

  &__nav-text {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: opacity var(--transition-fast);
    text-align: start;

    &--hidden {
      opacity: 0;
      width: 0;
    }
  }

  &__nav-badge {
    background-color: var(--color-primary-100);
    color: var(--color-primary-800);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    padding: 2px 6px;
    border-radius: var(--border-radius-full);
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;

    &--small {
      font-size: 10px;
      padding: 1px 4px;
      min-width: 16px;
      height: 16px;
    }
  }

  &__nav-chevron {
    flex-shrink: 0;
    color: var(--color-gray-400);
    transition: transform var(--transition-fast);
  }

  &__nav-sublist {
    list-style: none;
    margin: var(--spacing-1) 0 var(--spacing-2) 0;
    padding: 0;
    background-color: transparent;
  }

  &__nav-subitem {
    margin: 0 var(--spacing-3) 0 var(--spacing-8);

    &--hidden {
      display: none;
    }
  }

  &__nav-sublink {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: var(--spacing-2) var(--spacing-4);
    color: var(--color-gray-500);
    text-decoration: none;
    font-size: var(--font-size-sm);
    transition: all var(--transition-fast);
    border-radius: var(--border-radius-md);
    position: relative;
    min-height: 36px;
    gap: var(--spacing-2);

    &:hover {
      background-color: var(--color-gray-50);
      color: var(--color-gray-700);
    }

    &--active {
      background-color: var(--color-primary-50);
      color: var(--color-primary-700);
      font-weight: var(--font-weight-semibold);
    }
  }

  &__nav-subtext {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &__collapsed-indicator {
    position: absolute;
    bottom: var(--spacing-4);
    left: 50%;
    transform: translateX(-50%);
  }

  &__collapsed-dots {
    display: flex;
    gap: 4px;

    span {
      width: 4px;
      height: 4px;
      background-color: var(--color-gray-400);
      border-radius: 50%;
    }
  }

  &__resize-handle {
    position: absolute;
    top: 0;
    right: -2px;
    bottom: 0;
    width: 4px;
    cursor: col-resize;
    background-color: transparent;
    transition: background-color var(--transition-fast);

    &:hover {
      background-color: var(--color-primary-300);
    }
  }

  // Mobile responsive
  @media (max-width: 768px) {
    position: fixed;
    top: 64px;
    left: 0;
    bottom: 0;
    z-index: var(--z-index-modal);
    width: 100%;
    max-width: 320px;
    box-shadow: var(--shadow-lg);

    &--collapsed {
      transform: translateX(-100%);
    }
  }

  // Tablet responsive
  @media (max-width: 1024px) and (min-width: 769px) {
    width: 240px;

    &--collapsed {
      width: 56px;
    }

    &__nav-link {
      padding: var(--spacing-2) var(--spacing-4);

      .sidebar--collapsed & {
        padding: var(--spacing-2);
      }
    }

    &__nav-sublink {
      padding: var(--spacing-2) var(--spacing-4) var(--spacing-2) var(--spacing-10);
    }
  }
}
