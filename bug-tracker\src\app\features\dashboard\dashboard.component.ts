import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { AuthService } from '../../core/services/auth.service';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="dashboard">
      <!-- Header Section -->
      <div class="dashboard__header">
        <div class="dashboard__header-content">
          <div>
            <h1 class="dashboard__title">Welcome back, {{ (currentUser?.fullName || 'User').split(' ')[0] }}!</h1>
            <p class="dashboard__subtitle">Here's what's happening with your projects today.</p>
          </div>
          <div class="dashboard__actions">
            <button class="btn btn-primary">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="12" y1="5" x2="12" y2="19"></line>
                <line x1="5" y1="12" x2="19" y2="12"></line>
              </svg>
              New Project
            </button>
            <button class="btn btn-outline">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M8 2v4"></path>
                <path d="M16 2v4"></path>
                <rect width="16" height="16" x="4" y="4" rx="2"></rect>
                <path d="M9 9h6"></path>
                <path d="M9 13h6"></path>
              </svg>
              Report Bug
            </button>
          </div>
        </div>
      </div>

      <!-- KPI Cards -->
      <div class="dashboard__kpi-grid">
        <div class="kpi-card kpi-card--primary">
          <div class="kpi-card__icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path>
            </svg>
          </div>
          <div class="kpi-card__content">
            <div class="kpi-card__value">{{ metrics.totalProjects }}</div>
            <div class="kpi-card__label">Total Projects</div>
            <div class="kpi-card__change kpi-card__change--positive">
              <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="23,6 13.5,15.5 8.5,10.5 1,18"></polyline>
                <polyline points="17,6 23,6 23,12"></polyline>
              </svg>
              +2 this month
            </div>
          </div>
        </div>

        <div class="kpi-card kpi-card--danger">
          <div class="kpi-card__icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="15" y1="9" x2="9" y2="15"></line>
              <line x1="9" y1="9" x2="15" y2="15"></line>
            </svg>
          </div>
          <div class="kpi-card__content">
            <div class="kpi-card__value">{{ metrics.activeBugs }}</div>
            <div class="kpi-card__label">Active Bugs</div>
            <div class="kpi-card__change kpi-card__change--negative">
              <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="23,18 13.5,8.5 8.5,13.5 1,6"></polyline>
                <polyline points="17,18 23,18 23,12"></polyline>
              </svg>
              -5 from last week
            </div>
          </div>
        </div>

        <div class="kpi-card kpi-card--success">
          <div class="kpi-card__icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22,4 12,14.01 9,11.01"></polyline>
            </svg>
          </div>
          <div class="kpi-card__content">
            <div class="kpi-card__value">{{ metrics.resolvedBugs }}</div>
            <div class="kpi-card__label">Resolved Bugs</div>
            <div class="kpi-card__change kpi-card__change--positive">
              <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="23,6 13.5,15.5 8.5,10.5 1,18"></polyline>
                <polyline points="17,6 23,6 23,12"></polyline>
              </svg>
              +12 this week
            </div>
          </div>
        </div>

        <div class="kpi-card kpi-card--info">
          <div class="kpi-card__icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            </svg>
          </div>
          <div class="kpi-card__content">
            <div class="kpi-card__value">{{ metrics.teamMembers }}</div>
            <div class="kpi-card__label">Team Members</div>
            <div class="kpi-card__change kpi-card__change--neutral">
              Across all projects
            </div>
          </div>
        </div>
      </div>

      <!-- Charts and Analytics Section -->
      <div class="dashboard__charts-grid">
        <!-- Bug Trends Chart -->
        <div class="chart-card">
          <div class="chart-card__header">
            <h3 class="chart-card__title">Bug Trends</h3>
            <div class="chart-card__actions">
              <select class="form-control form-control-sm">
                <option>Last 7 days</option>
                <option>Last 30 days</option>
                <option>Last 3 months</option>
              </select>
            </div>
          </div>
          <div class="chart-card__content">
            <div class="chart-placeholder">
              <svg class="chart-svg" viewBox="0 0 400 200">
                <!-- Mock line chart -->
                <defs>
                  <linearGradient id="bugGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                    <stop offset="0%" style="stop-color:var(--color-primary-500);stop-opacity:0.3" />
                    <stop offset="100%" style="stop-color:var(--color-primary-500);stop-opacity:0" />
                  </linearGradient>
                </defs>
                <polyline fill="none" stroke="var(--color-primary-600)" stroke-width="3"
                  points="20,150 60,120 100,140 140,100 180,110 220,80 260,90 300,60 340,70 380,50"/>
                <polyline fill="url(#bugGradient)" stroke="none"
                  points="20,150 60,120 100,140 140,100 180,110 220,80 260,90 300,60 340,70 380,50 380,180 20,180"/>
              </svg>
            </div>
            <div class="chart-legend">
              <div class="chart-legend__item">
                <span class="chart-legend__color" style="background-color: var(--color-primary-600);"></span>
                <span class="chart-legend__label">Bugs Reported</span>
              </div>
              <div class="chart-legend__item">
                <span class="chart-legend__color" style="background-color: var(--color-success-600);"></span>
                <span class="chart-legend__label">Bugs Resolved</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Priority Distribution -->
        <div class="chart-card">
          <div class="chart-card__header">
            <h3 class="chart-card__title">Bug Priority Distribution</h3>
          </div>
          <div class="chart-card__content">
            <div class="priority-chart">
              <div class="priority-item">
                <div class="priority-item__bar">
                  <div class="priority-item__fill priority-item__fill--critical" style="width: 15%;"></div>
                </div>
                <div class="priority-item__label">
                  <span class="priority-item__name">Critical</span>
                  <span class="priority-item__value">3</span>
                </div>
              </div>
              <div class="priority-item">
                <div class="priority-item__bar">
                  <div class="priority-item__fill priority-item__fill--high" style="width: 35%;"></div>
                </div>
                <div class="priority-item__label">
                  <span class="priority-item__name">High</span>
                  <span class="priority-item__value">8</span>
                </div>
              </div>
              <div class="priority-item">
                <div class="priority-item__bar">
                  <div class="priority-item__fill priority-item__fill--medium" style="width: 60%;"></div>
                </div>
                <div class="priority-item__label">
                  <span class="priority-item__name">Medium</span>
                  <span class="priority-item__value">12</span>
                </div>
              </div>
              <div class="priority-item">
                <div class="priority-item__bar">
                  <div class="priority-item__fill priority-item__fill--low" style="width: 25%;"></div>
                </div>
                <div class="priority-item__label">
                  <span class="priority-item__name">Low</span>
                  <span class="priority-item__value">5</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Content Grid -->
      <div class="dashboard__content-grid">
        <!-- Recent Activity -->
        <div class="activity-card">
          <div class="activity-card__header">
            <h3 class="activity-card__title">Recent Activity</h3>
            <a routerLink="/bugs" class="activity-card__link">View all</a>
          </div>
          <div class="activity-card__content">
            <div class="activity-list">
              <div *ngFor="let activity of recentActivities" class="activity-item">
                <div class="activity-item__avatar" [ngClass]="'activity-item__avatar--' + activity.type">
                  <span>{{ activity.userInitials }}</span>
                </div>
                <div class="activity-item__content">
                  <div class="activity-item__title">{{ activity.title }}</div>
                  <div class="activity-item__description">{{ activity.description }}</div>
                  <div class="activity-item__time">{{ activity.timeAgo }}</div>
                </div>
                <div class="activity-item__status">
                  <span class="badge" [ngClass]="'badge-' + activity.statusColor">{{ activity.status }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions-card">
          <div class="quick-actions-card__header">
            <h3 class="quick-actions-card__title">Quick Actions</h3>
          </div>
          <div class="quick-actions-card__content">
            <div class="quick-actions-grid">
              <a routerLink="/projects/create" class="quick-action">
                <div class="quick-action__icon quick-action__icon--primary">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path>
                  </svg>
                </div>
                <div class="quick-action__content">
                  <div class="quick-action__title">New Project</div>
                  <div class="quick-action__description">Create a new project</div>
                </div>
              </a>

              <a routerLink="/bugs/create" class="quick-action">
                <div class="quick-action__icon quick-action__icon--danger">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M8 2v4"></path>
                    <path d="M16 2v4"></path>
                    <rect width="16" height="16" x="4" y="4" rx="2"></rect>
                    <path d="M9 9h6"></path>
                    <path d="M9 13h6"></path>
                  </svg>
                </div>
                <div class="quick-action__content">
                  <div class="quick-action__title">Report Bug</div>
                  <div class="quick-action__description">Report a new bug</div>
                </div>
              </a>

              <a routerLink="/reports" class="quick-action">
                <div class="quick-action__icon quick-action__icon--info">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M3 3v18h18"></path>
                    <path d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3"></path>
                  </svg>
                </div>
                <div class="quick-action__content">
                  <div class="quick-action__title">View Reports</div>
                  <div class="quick-action__description">Analytics & insights</div>
                </div>
              </a>

              <a routerLink="/admin" class="quick-action">
                <div class="quick-action__icon quick-action__icon--warning">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="3"></circle>
                    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                  </svg>
                </div>
                <div class="quick-action__content">
                  <div class="quick-action__title">Admin Panel</div>
                  <div class="quick-action__description">System management</div>
                </div>
              </a>
            </div>
          </div>
        </div>

        <!-- My Tasks -->
        <div class="tasks-card">
          <div class="tasks-card__header">
            <h3 class="tasks-card__title">My Tasks</h3>
            <span class="tasks-card__count">{{ myTasks.length }}</span>
          </div>
          <div class="tasks-card__content">
            <div class="task-list">
              <div *ngFor="let task of myTasks" class="task-item">
                <div class="task-item__checkbox">
                  <input type="checkbox" [checked]="task.completed" class="form-checkbox">
                </div>
                <div class="task-item__content">
                  <div class="task-item__title" [ngClass]="{'task-item__title--completed': task.completed}">
                    {{ task.title }}
                  </div>
                  <div class="task-item__meta">
                    <span class="task-item__project">{{ task.project }}</span>
                    <span class="task-item__priority" [ngClass]="'task-item__priority--' + task.priority">
                      {{ task.priority }}
                    </span>
                  </div>
                </div>
                <div class="task-item__due">
                  <span class="task-item__due-date" [ngClass]="{'task-item__due-date--overdue': task.overdue}">
                    {{ task.dueDate }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .dashboard {
      padding: 0;
      background: transparent;
      min-height: calc(100vh - 72px);
    }

    /* Header Section */
    .dashboard__header {
      margin-bottom: var(--spacing-6);
      background: var(--color-white);
      border-radius: var(--border-radius-lg);
      padding: var(--spacing-6);
      border: 1px solid var(--color-gray-200);
    }

    .dashboard__header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      flex-wrap: wrap;
      gap: var(--spacing-6);
    }

    .dashboard__title {
      font-size: var(--font-size-4xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-gray-900);
      margin: 0;
      line-height: 1.2;
    }

    .dashboard__subtitle {
      color: var(--color-gray-600);
      font-size: var(--font-size-lg);
      margin: var(--spacing-2) 0 0 0;
      font-weight: var(--font-weight-normal);
    }

    .dashboard__actions {
      display: flex;
      gap: var(--spacing-3);
      align-items: center;
    }

    /* KPI Cards Grid */
    .dashboard__kpi-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: var(--spacing-4);
      margin-bottom: var(--spacing-6);
    }

    .kpi-card {
      background: var(--color-white);
      border-radius: var(--border-radius-lg);
      padding: var(--spacing-5);
      border: 1px solid var(--color-gray-200);
      position: relative;
      overflow: hidden;
      transition: all var(--transition-base);
      display: flex;
      align-items: center;
      gap: var(--spacing-4);
    }

    .kpi-card:hover {
      border-color: var(--color-gray-300);
    }

    .kpi-card--primary {
      --accent-color: var(--color-primary-600);
      --accent-color-light: var(--color-primary-400);
    }

    .kpi-card--danger {
      --accent-color: var(--color-error-600);
      --accent-color-light: var(--color-error-400);
    }

    .kpi-card--success {
      --accent-color: var(--color-success-600);
      --accent-color-light: var(--color-success-400);
    }

    .kpi-card--info {
      --accent-color: var(--color-info-600);
      --accent-color-light: var(--color-info-400);
    }

    .kpi-card__icon {
      width: 48px;
      height: 48px;
      border-radius: var(--border-radius-lg);
      background: linear-gradient(135deg, var(--accent-color), var(--accent-color-light));
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--color-white);
      flex-shrink: 0;
    }

    .kpi-card__content {
      flex: 1;
    }

    .kpi-card__value {
      font-size: var(--font-size-3xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-gray-900);
      line-height: 1.1;
      margin-bottom: var(--spacing-1);
    }

    .kpi-card__label {
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-600);
      margin-bottom: var(--spacing-2);
    }

    .kpi-card__change {
      display: flex;
      align-items: center;
      gap: var(--spacing-1);
      font-size: var(--font-size-xs);
      font-weight: var(--font-weight-medium);
    }

    .kpi-card__change--positive {
      color: var(--color-success-600);
    }

    .kpi-card__change--negative {
      color: var(--color-error-600);
    }

    .kpi-card__change--neutral {
      color: var(--color-gray-500);
    }

    /* Charts Grid */
    .dashboard__charts-grid {
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: var(--spacing-4);
      margin-bottom: var(--spacing-6);
    }

    .chart-card {
      background: var(--color-white);
      border-radius: var(--border-radius-lg);
      border: 1px solid var(--color-gray-200);
      overflow: hidden;
      transition: all var(--transition-base);
    }

    .chart-card:hover {
      border-color: var(--color-gray-300);
    }

    .chart-card__header {
      padding: var(--spacing-6);
      border-bottom: 1px solid var(--color-gray-100);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .chart-card__title {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-900);
      margin: 0;
    }

    .chart-card__content {
      padding: var(--spacing-6);
    }

    .chart-placeholder {
      height: 200px;
      margin-bottom: var(--spacing-4);
    }

    .chart-svg {
      width: 100%;
      height: 100%;
    }

    .chart-legend {
      display: flex;
      gap: var(--spacing-4);
      justify-content: center;
    }

    .chart-legend__item {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
    }

    .chart-legend__color {
      width: 12px;
      height: 12px;
      border-radius: var(--border-radius-full);
    }

    .chart-legend__label {
      font-size: var(--font-size-sm);
      color: var(--color-gray-600);
    }

    /* Priority Chart */
    .priority-chart {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-4);
    }

    .priority-item {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-2);
    }

    .priority-item__bar {
      height: 8px;
      background: var(--color-gray-100);
      border-radius: var(--border-radius-full);
      overflow: hidden;
    }

    .priority-item__fill {
      height: 100%;
      border-radius: var(--border-radius-full);
      transition: width var(--transition-base);
    }

    .priority-item__fill--critical {
      background: linear-gradient(90deg, var(--color-error-600), var(--color-error-500));
    }

    .priority-item__fill--high {
      background: linear-gradient(90deg, var(--color-warning-600), var(--color-warning-500));
    }

    .priority-item__fill--medium {
      background: linear-gradient(90deg, var(--color-info-600), var(--color-info-500));
    }

    .priority-item__fill--low {
      background: linear-gradient(90deg, var(--color-success-600), var(--color-success-500));
    }

    .priority-item__label {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .priority-item__name {
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-700);
    }

    .priority-item__value {
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-bold);
      color: var(--color-gray-900);
    }

    /* Content Grid */
    .dashboard__content-grid {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: var(--spacing-4);
    }

    /* Activity Card */
    .activity-card {
      background: var(--color-white);
      border-radius: var(--border-radius-lg);
      border: 1px solid var(--color-gray-200);
      overflow: hidden;
      transition: all var(--transition-base);
    }

    .activity-card:hover {
      border-color: var(--color-gray-300);
    }

    .activity-card__header {
      padding: var(--spacing-6);
      border-bottom: 1px solid var(--color-gray-100);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .activity-card__title {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-900);
      margin: 0;
    }

    .activity-card__link {
      font-size: var(--font-size-sm);
      color: var(--color-primary-600);
      text-decoration: none;
      font-weight: var(--font-weight-medium);
    }

    .activity-card__link:hover {
      color: var(--color-primary-700);
    }

    .activity-card__content {
      padding: var(--spacing-6);
    }

    .activity-list {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-4);
    }

    .activity-item {
      display: flex;
      align-items: flex-start;
      gap: var(--spacing-3);
    }

    .activity-item__avatar {
      width: 40px;
      height: 40px;
      border-radius: var(--border-radius-full);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      color: var(--color-white);
      flex-shrink: 0;
    }

    .activity-item__avatar--bug {
      background: linear-gradient(135deg, var(--color-error-500), var(--color-error-600));
    }

    .activity-item__avatar--project {
      background: linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600));
    }

    .activity-item__avatar--resolved {
      background: linear-gradient(135deg, var(--color-success-500), var(--color-success-600));
    }

    .activity-item__content {
      flex: 1;
      min-width: 0;
    }

    .activity-item__title {
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-900);
      margin-bottom: var(--spacing-1);
    }

    .activity-item__description {
      font-size: var(--font-size-xs);
      color: var(--color-gray-600);
      margin-bottom: var(--spacing-1);
    }

    .activity-item__time {
      font-size: var(--font-size-xs);
      color: var(--color-gray-500);
    }

    .activity-item__status {
      flex-shrink: 0;
    }

    /* Quick Actions Card */
    .quick-actions-card {
      background: var(--color-white);
      border-radius: var(--border-radius-lg);
      border: 1px solid var(--color-gray-200);
      overflow: hidden;
    }

    .quick-actions-card__header {
      padding: var(--spacing-6);
      border-bottom: 1px solid var(--color-gray-100);
    }

    .quick-actions-card__title {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-900);
      margin: 0;
    }

    .quick-actions-card__content {
      padding: var(--spacing-6);
    }

    .quick-actions-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-4);
    }

    .quick-action {
      display: flex;
      align-items: center;
      gap: var(--spacing-3);
      padding: var(--spacing-4);
      border-radius: var(--border-radius-lg);
      border: 1px solid var(--color-gray-200);
      text-decoration: none;
      transition: all var(--transition-fast);
    }

    .quick-action:hover {
      border-color: var(--color-primary-300);
      background: var(--color-primary-50);
      transform: translateY(-1px);
    }

    .quick-action__icon {
      width: 40px;
      height: 40px;
      border-radius: var(--border-radius-lg);
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--color-white);
      flex-shrink: 0;
    }

    .quick-action__icon--primary {
      background: linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600));
    }

    .quick-action__icon--danger {
      background: linear-gradient(135deg, var(--color-error-500), var(--color-error-600));
    }

    .quick-action__icon--info {
      background: linear-gradient(135deg, var(--color-info-500), var(--color-info-600));
    }

    .quick-action__icon--warning {
      background: linear-gradient(135deg, var(--color-warning-500), var(--color-warning-600));
    }

    .quick-action__content {
      flex: 1;
      min-width: 0;
    }

    .quick-action__title {
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-900);
      margin-bottom: var(--spacing-1);
    }

    .quick-action__description {
      font-size: var(--font-size-xs);
      color: var(--color-gray-600);
    }

    /* Tasks Card */
    .tasks-card {
      background: var(--color-white);
      border-radius: var(--border-radius-lg);
      border: 1px solid var(--color-gray-200);
      overflow: hidden;
    }

    .tasks-card__header {
      padding: var(--spacing-6);
      border-bottom: 1px solid var(--color-gray-100);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .tasks-card__title {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-900);
      margin: 0;
    }

    .tasks-card__count {
      background: var(--color-primary-100);
      color: var(--color-primary-800);
      font-size: var(--font-size-xs);
      font-weight: var(--font-weight-bold);
      padding: var(--spacing-1) var(--spacing-2);
      border-radius: var(--border-radius-full);
    }

    .tasks-card__content {
      padding: var(--spacing-6);
    }

    .task-list {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-4);
    }

    .task-item {
      display: flex;
      align-items: flex-start;
      gap: var(--spacing-3);
    }

    .task-item__checkbox {
      margin-top: 2px;
    }

    .task-item__content {
      flex: 1;
      min-width: 0;
    }

    .task-item__title {
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-900);
      margin-bottom: var(--spacing-1);
    }

    .task-item__title--completed {
      text-decoration: line-through;
      color: var(--color-gray-500);
    }

    .task-item__meta {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
    }

    .task-item__project {
      font-size: var(--font-size-xs);
      color: var(--color-gray-600);
    }

    .task-item__priority {
      font-size: var(--font-size-xs);
      font-weight: var(--font-weight-medium);
      padding: 2px 6px;
      border-radius: var(--border-radius-base);
    }

    .task-item__priority--high {
      background: var(--color-error-100);
      color: var(--color-error-800);
    }

    .task-item__priority--medium {
      background: var(--color-warning-100);
      color: var(--color-warning-800);
    }

    .task-item__priority--low {
      background: var(--color-success-100);
      color: var(--color-success-800);
    }

    .task-item__due {
      flex-shrink: 0;
    }

    .task-item__due-date {
      font-size: var(--font-size-xs);
      color: var(--color-gray-600);
    }

    .task-item__due-date--overdue {
      color: var(--color-error-600);
      font-weight: var(--font-weight-medium);
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
      .dashboard__charts-grid {
        grid-template-columns: 1fr;
      }

      .dashboard__content-grid {
        grid-template-columns: 1fr 1fr;
      }
    }

    @media (max-width: 768px) {
      .dashboard__header {
        padding: var(--spacing-4);
        margin-bottom: var(--spacing-4);
      }

      .dashboard__header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-3);
      }

      .dashboard__title {
        font-size: var(--font-size-3xl);
      }

      .dashboard__kpi-grid {
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-3);
        margin-bottom: var(--spacing-4);
      }

      .dashboard__charts-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-3);
        margin-bottom: var(--spacing-4);
      }

      .dashboard__content-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-3);
      }

      .quick-actions-grid {
        grid-template-columns: 1fr;
      }
    }

    @media (max-width: 480px) {
      .dashboard__header {
        padding: var(--spacing-3);
      }

      .dashboard__title {
        font-size: var(--font-size-2xl);
      }

      .dashboard__kpi-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-3);
      }

      .kpi-card {
        padding: var(--spacing-4);
      }
    }
  `]
})
export class DashboardComponent implements OnInit {
  private authService = inject(AuthService);

  currentUser = this.authService.getCurrentUser();

  metrics = {
    totalProjects: 12,
    activeBugs: 23,
    resolvedBugs: 156,
    teamMembers: 8
  };

  recentActivities = [
    {
      type: 'bug',
      userInitials: 'JD',
      title: 'John Doe reported a new bug',
      description: 'Login form validation error in user authentication module',
      timeAgo: '2 hours ago',
      status: 'Open',
      statusColor: 'error'
    },
    {
      type: 'resolved',
      userInitials: 'JS',
      title: 'Jane Smith resolved a bug',
      description: 'Dashboard loading issue has been fixed and tested',
      timeAgo: '4 hours ago',
      status: 'Resolved',
      statusColor: 'success'
    },
    {
      type: 'project',
      userInitials: 'MW',
      title: 'Mike Wilson created a new project',
      description: 'E-commerce Platform v2.0 project has been initialized',
      timeAgo: '1 day ago',
      status: 'Active',
      statusColor: 'primary'
    },
    {
      type: 'bug',
      userInitials: 'SJ',
      title: 'Sarah Johnson updated bug priority',
      description: 'Payment gateway timeout issue marked as critical',
      timeAgo: '2 days ago',
      status: 'Critical',
      statusColor: 'error'
    }
  ];

  myTasks = [
    {
      title: 'Fix authentication bug',
      project: 'User Management',
      priority: 'high',
      dueDate: 'Today',
      completed: false,
      overdue: true
    },
    {
      title: 'Review pull request #234',
      project: 'E-commerce Platform',
      priority: 'medium',
      dueDate: 'Tomorrow',
      completed: false,
      overdue: false
    },
    {
      title: 'Update documentation',
      project: 'API Gateway',
      priority: 'low',
      dueDate: 'Next week',
      completed: true,
      overdue: false
    },
    {
      title: 'Test payment integration',
      project: 'Payment System',
      priority: 'high',
      dueDate: 'Friday',
      completed: false,
      overdue: false
    }
  ];

  ngOnInit() {
    // Load dashboard data
    this.loadDashboardData();
  }

  private loadDashboardData() {
    // In a real application, this would load data from services
    // For now, we're using mock data
  }
}
