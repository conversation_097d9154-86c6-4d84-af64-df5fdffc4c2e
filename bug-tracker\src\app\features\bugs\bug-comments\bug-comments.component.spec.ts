import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { of, throwError } from 'rxjs';

import { BugCommentsComponent } from './bug-comments.component';
import { BugService } from '../../../core/services/bug.service';
import { UserService } from '../../../core/services/user.service';
import { AuthService } from '../../../core/services/auth.service';
import { BugComment } from '../../../core/models/bug.model';
import { UserRole } from '../../../core/models/user.model';

describe('BugCommentsComponent', () => {
  let component: BugCommentsComponent;
  let fixture: ComponentFixture<BugCommentsComponent>;
  let bugService: jasmine.SpyObj<BugService>;
  let userService: jasmine.SpyObj<UserService>;
  let authService: jasmine.SpyObj<AuthService>;

  const mockUser = {
    id: 'user-1',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    fullName: 'Test User',
    role: UserRole.DEVELOPER,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  const mockComment: BugComment = {
    id: 'comment-1',
    bugId: 'bug-1',
    content: 'This is a test comment',
    authorId: 'user-1',
    author: mockUser,
    createdAt: new Date(),
    updatedAt: new Date(),
    isInternal: false,
    mentionedUsers: [],
    attachments: []
  };

  beforeEach(async () => {
    const bugServiceSpy = jasmine.createSpyObj('BugService', [
      'addComment', 
      'updateComment', 
      'deleteComment'
    ]);
    const userServiceSpy = jasmine.createSpyObj('UserService', ['getAllUsers']);
    const authServiceSpy = jasmine.createSpyObj('AuthService', ['getCurrentUser']);

    await TestBed.configureTestingModule({
      imports: [
        BugCommentsComponent,
        ReactiveFormsModule
      ],
      providers: [
        { provide: BugService, useValue: bugServiceSpy },
        { provide: UserService, useValue: userServiceSpy },
        { provide: AuthService, useValue: authServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(BugCommentsComponent);
    component = fixture.componentInstance;
    
    bugService = TestBed.inject(BugService) as jasmine.SpyObj<BugService>;
    userService = TestBed.inject(UserService) as jasmine.SpyObj<UserService>;
    authService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;

    // Setup default spy returns
    userService.getAllUsers.and.returnValue([mockUser]);
    authService.getCurrentUser.and.returnValue(mockUser);

    // Set component inputs
    component.bugId = 'bug-1';
    component.comments = [mockComment];
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form on init', () => {
    fixture.detectChanges();
    
    expect(component.commentForm).toBeDefined();
    expect(component.commentForm.get('content')?.value).toBe('');
    expect(component.commentForm.get('isInternal')?.value).toBe(false);
  });

  it('should load users on init', () => {
    fixture.detectChanges();
    
    expect(userService.getAllUsers).toHaveBeenCalled();
    expect(component.allUsers.length).toBe(1);
    expect(component.allUsers[0].fullName).toBe('Test User');
  });

  it('should validate comment form', () => {
    fixture.detectChanges();
    
    const contentControl = component.commentForm.get('content');
    
    expect(contentControl?.valid).toBeFalsy();
    
    contentControl?.setValue('ab'); // Less than 3 characters
    expect(contentControl?.hasError('minlength')).toBeTruthy();
    
    contentControl?.setValue('abc'); // Exactly 3 characters
    expect(contentControl?.valid).toBeTruthy();
  });

  it('should add comment successfully', () => {
    bugService.addComment.and.returnValue(of({
      success: true,
      data: mockComment,
      message: 'Comment added',
      timestamp: new Date()
    }));
    
    fixture.detectChanges();
    
    component.commentForm.patchValue({
      content: 'New test comment',
      isInternal: false
    });
    
    component.onSubmitComment();
    
    expect(bugService.addComment).toHaveBeenCalledWith('bug-1', 'New test comment', false);
    expect(component.commentForm.get('content')?.value).toBe('');
  });

  it('should add internal comment', () => {
    bugService.addComment.and.returnValue(of({
      success: true,
      data: { ...mockComment, isInternal: true },
      message: 'Internal comment added',
      timestamp: new Date()
    }));
    
    fixture.detectChanges();
    
    component.commentForm.patchValue({
      content: 'Internal comment',
      isInternal: true
    });
    
    component.onSubmitComment();
    
    expect(bugService.addComment).toHaveBeenCalledWith('bug-1', 'Internal comment', true);
  });

  it('should handle comment submission error', () => {
    bugService.addComment.and.returnValue(throwError(() => new Error('Failed to add comment')));
    
    fixture.detectChanges();
    
    component.commentForm.patchValue({
      content: 'Test comment',
      isInternal: false
    });
    
    component.onSubmitComment();
    
    expect(component.errorMessage).toContain('Failed to add comment');
    expect(component.submitting).toBeFalsy();
  });

  it('should not submit invalid form', () => {
    fixture.detectChanges();
    
    // Leave content empty (invalid)
    component.onSubmitComment();
    
    expect(bugService.addComment).not.toHaveBeenCalled();
  });

  it('should cancel comment', () => {
    fixture.detectChanges();
    
    component.commentForm.patchValue({
      content: 'Some content',
      isInternal: true
    });
    
    component.cancelComment();
    
    expect(component.commentForm.get('content')?.value).toBe('');
    expect(component.commentForm.get('isInternal')?.value).toBe(false);
    expect(component.showMentionSuggestions).toBeFalsy();
  });

  it('should check if user can add comment', () => {
    fixture.detectChanges();
    
    expect(component.canAddComment()).toBeTruthy();
    
    // No current user
    authService.getCurrentUser.and.returnValue(null);
    component.currentUser = null;
    
    expect(component.canAddComment()).toBeFalsy();
  });

  it('should check if user can edit comment', () => {
    fixture.detectChanges();
    
    // User can edit their own comment
    expect(component.canEditComment(mockComment)).toBeTruthy();
    
    // User cannot edit another user's comment
    const otherUserComment = { ...mockComment, authorId: 'other-user' };
    expect(component.canEditComment(otherUserComment)).toBeFalsy();
    
    // No current user
    authService.getCurrentUser.and.returnValue(null);
    component.currentUser = null;
    expect(component.canEditComment(mockComment)).toBeFalsy();
  });

  it('should edit comment', () => {
    bugService.updateComment.and.returnValue(of({
      success: true,
      data: { ...mockComment, content: 'Updated comment' },
      message: 'Comment updated',
      timestamp: new Date()
    }));
    
    spyOn(window, 'prompt').and.returnValue('Updated comment');
    fixture.detectChanges();
    
    component.editComment(mockComment);
    
    expect(window.prompt).toHaveBeenCalledWith('Edit comment:', 'This is a test comment');
    expect(bugService.updateComment).toHaveBeenCalledWith('bug-1', 'comment-1', 'Updated comment');
  });

  it('should not edit comment if user cancels prompt', () => {
    spyOn(window, 'prompt').and.returnValue(null);
    fixture.detectChanges();
    
    component.editComment(mockComment);
    
    expect(bugService.updateComment).not.toHaveBeenCalled();
  });

  it('should not edit comment if content is unchanged', () => {
    spyOn(window, 'prompt').and.returnValue('This is a test comment'); // Same as original
    fixture.detectChanges();
    
    component.editComment(mockComment);
    
    expect(bugService.updateComment).not.toHaveBeenCalled();
  });

  it('should delete comment with confirmation', () => {
    bugService.deleteComment.and.returnValue(of({
      success: true,
      data: undefined,
      message: 'Comment deleted',
      timestamp: new Date()
    }));
    
    spyOn(window, 'confirm').and.returnValue(true);
    fixture.detectChanges();
    
    component.deleteComment(mockComment);
    
    expect(window.confirm).toHaveBeenCalled();
    expect(bugService.deleteComment).toHaveBeenCalledWith('bug-1', 'comment-1');
  });

  it('should not delete comment if user cancels', () => {
    spyOn(window, 'confirm').and.returnValue(false);
    fixture.detectChanges();
    
    component.deleteComment(mockComment);
    
    expect(bugService.deleteComment).not.toHaveBeenCalled();
  });

  it('should handle mention input', () => {
    fixture.detectChanges();
    
    // Simulate typing @mention
    component.commentForm.get('content')?.setValue('Hello @test');
    
    expect(component.showMentionSuggestions).toBeTruthy();
    expect(component.mentionSuggestions.length).toBeGreaterThan(0);
  });

  it('should select mention', () => {
    fixture.detectChanges();
    
    component.commentForm.get('content')?.setValue('Hello @te');
    component.selectMention(mockUser);
    
    expect(component.commentForm.get('content')?.value).toBe('Hello @Test User ');
    expect(component.showMentionSuggestions).toBeFalsy();
  });

  it('should format comment time correctly', () => {
    const now = new Date();
    const oneMinuteAgo = new Date(now.getTime() - 60 * 1000);
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    
    expect(component.formatCommentTime(now)).toBe('Just now');
    expect(component.formatCommentTime(oneMinuteAgo)).toBe('1m ago');
    expect(component.formatCommentTime(oneHourAgo)).toBe('1h ago');
    expect(component.formatCommentTime(oneDayAgo)).toBe('1d ago');
  });

  it('should format comment content with mentions', () => {
    const contentWithMention = 'Hello @John Doe, please check this';
    const formattedContent = component.formatCommentContent(contentWithMention);
    
    expect(formattedContent).toContain('<span class="mention">@John Doe</span>');
  });

  it('should format file size correctly', () => {
    expect(component.formatFileSize(0)).toBe('0 Bytes');
    expect(component.formatFileSize(1024)).toBe('1 KB');
    expect(component.formatFileSize(1024 * 1024)).toBe('1 MB');
    expect(component.formatFileSize(1024 * 1024 * 1024)).toBe('1 GB');
  });

  it('should get current user initials', () => {
    fixture.detectChanges();
    
    expect(component.getCurrentUserInitials()).toBe('TU');
  });

  it('should get user initials', () => {
    expect(component.getUserInitials(mockUser)).toBe('TU');
  });

  it('should track comments by id', () => {
    const trackResult = component.trackByCommentId(0, mockComment);
    expect(trackResult).toBe('comment-1');
  });

  it('should check field validity', () => {
    fixture.detectChanges();
    
    const contentControl = component.commentForm.get('content');
    
    expect(component.isFieldInvalid('content')).toBeFalsy();
    
    contentControl?.markAsTouched();
    expect(component.isFieldInvalid('content')).toBeTruthy();
    
    contentControl?.setValue('Valid content');
    expect(component.isFieldInvalid('content')).toBeFalsy();
  });

  it('should handle retry load', () => {
    fixture.detectChanges();
    
    component.errorMessage = 'Some error';
    component.retryLoadComments();
    
    expect(component.errorMessage).toBe('');
  });

  it('should display comments correctly', () => {
    fixture.detectChanges();
    
    const compiled = fixture.nativeElement;
    
    expect(compiled.querySelector('.comment-text')?.textContent).toContain('This is a test comment');
    expect(compiled.querySelector('.comment-author')?.textContent).toContain('Test User');
  });

  it('should show empty state when no comments', () => {
    component.comments = [];
    fixture.detectChanges();
    
    const compiled = fixture.nativeElement;
    const emptyElement = compiled.querySelector('.comments-empty');
    
    expect(emptyElement).toBeTruthy();
    expect(emptyElement.textContent).toContain('No comments yet');
  });

  it('should show loading state', () => {
    component.loading = true;
    fixture.detectChanges();
    
    const compiled = fixture.nativeElement;
    const loadingElement = compiled.querySelector('.comments-loading');
    
    expect(loadingElement).toBeTruthy();
    expect(loadingElement.textContent).toContain('Loading comments');
  });

  it('should show error state', () => {
    component.errorMessage = 'Failed to load comments';
    fixture.detectChanges();
    
    const compiled = fixture.nativeElement;
    const errorElement = compiled.querySelector('.comments-error');
    
    expect(errorElement).toBeTruthy();
    expect(errorElement.textContent).toContain('Failed to load comments');
  });
});
