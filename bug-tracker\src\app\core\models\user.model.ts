export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  fullName: string;
  role: UserRole;
  avatar?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  lastLoginAt?: Date;
}

export enum UserRole {
  ADMIN = 'Admin',
  PROJECT_MANAGER = 'Project Manager',
  DEVELOPER = 'Developer',
  QA_ENGINEER = 'QA Engineer',
  QA_LEAD = 'QA Lead',
  DEV_LEAD = 'Dev Lead',
  BUSINESS_ANALYST = 'Business Analyst'
}

export interface CreateUserRequest {
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  password: string;
}

export interface UpdateUserRequest {
  firstName?: string;
  lastName?: string;
  role?: UserRole;
  isActive?: boolean;
}

export interface UserProfile {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  fullName: string;
  role: UserRole;
  avatar?: string;
  preferences: UserPreferences;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  timezone: string;
  emailNotifications: boolean;
  browserNotifications: boolean;
  dashboardLayout: 'grid' | 'list';
  projectsViewMode: 'grid' | 'table';
}

export interface AuthUser {
  id: string;
  email: string;
  fullName: string;
  role: UserRole;
  avatar?: string;
  permissions: string[];
  accessToken: string;
  refreshToken: string;
}

export interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface LoginResponse {
  user: AuthUser;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

// Role-based permissions
export const ROLE_PERMISSIONS: Record<UserRole, string[]> = {
  [UserRole.ADMIN]: [
    'users.create',
    'users.read',
    'users.update',
    'users.delete',
    'projects.create',
    'projects.read',
    'projects.update',
    'projects.delete',
    'bugs.create',
    'bugs.read',
    'bugs.update',
    'bugs.delete',
    'reports.read',
    'reports.export',
    'settings.read',
    'settings.update'
  ],
  [UserRole.PROJECT_MANAGER]: [
    'projects.create',
    'projects.read',
    'projects.update',
    'bugs.create',
    'bugs.read',
    'bugs.update',
    'bugs.assign',
    'reports.read',
    'reports.export'
  ],
  [UserRole.DEVELOPER]: [
    'projects.read',
    'bugs.create',
    'bugs.read',
    'bugs.update',
    'bugs.comment'
  ],
  [UserRole.QA_ENGINEER]: [
    'projects.read',
    'bugs.create',
    'bugs.read',
    'bugs.update',
    'bugs.test',
    'bugs.comment'
  ],
  [UserRole.BUSINESS_ANALYST]: [
    'projects.read',
    'bugs.create',
    'bugs.read',
    'bugs.comment',
    'reports.read'
  ]
};

// Helper functions
export function hasPermission(userRole: UserRole, permission: string): boolean {
  return ROLE_PERMISSIONS[userRole]?.includes(permission) || false;
}

export function getUserInitials(user: User | AuthUser): string {
  const firstName = 'firstName' in user ? user.firstName : user.fullName.split(' ')[0];
  const lastName = 'lastName' in user ? user.lastName : user.fullName.split(' ')[1] || '';
  return `${firstName[0]}${lastName[0] || ''}`.toUpperCase();
}

export function formatUserName(user: User | AuthUser): string {
  if ('fullName' in user) {
    return user.fullName;
  }
  return `${(user as User).firstName} ${(user as User).lastName}`;
}
