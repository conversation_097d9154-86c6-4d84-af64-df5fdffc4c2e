import { Component, Input, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';

interface NavigationItem {
  label: string;
  icon: string;
  route?: string;
  children?: NavigationItem[];
  badge?: string | number;
  roles?: string[];
}

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './sidebar.component.html',
  styleUrl: './sidebar.component.scss'
})
export class SidebarComponent {
  @Input() collapsed = false;

  private router = inject(Router);

  // Mock current user role - will be replaced with real auth service
  currentUserRole = 'Admin';

  navigationItems: NavigationItem[] = [
    {
      label: 'Dashboard',
      icon: 'dashboard',
      route: '/dashboard'
    },
    {
      label: 'Projects',
      icon: 'projects',
      children: [
        { label: 'All Projects', icon: 'list', route: '/projects' },
        { label: 'Create Project', icon: 'plus', route: '/projects/create', roles: ['Admin', 'Project Manager'] },
        { label: 'My Projects', icon: 'user', route: '/projects/my' }
      ]
    },
    {
      label: 'Bugs',
      icon: 'bugs',
      children: [
        { label: 'All Bugs', icon: 'list', route: '/bugs', badge: '23' },
        { label: 'Report Bug', icon: 'plus', route: '/bugs/create' },
        { label: 'My Bugs', icon: 'user', route: '/bugs/my' },
        { label: 'Assigned to Me', icon: 'assign', route: '/bugs/assigned', badge: '5' }
      ]
    },
    {
      label: 'Reports',
      icon: 'reports',
      children: [
        { label: 'Bug Reports', icon: 'chart', route: '/reports/bugs' },
        { label: 'Project Reports', icon: 'chart', route: '/reports/projects' },
        { label: 'Team Performance', icon: 'team', route: '/reports/team', roles: ['Admin', 'Project Manager'] },
        { label: 'Custom Reports', icon: 'settings', route: '/reports/custom', roles: ['Admin'] }
      ]
    },
    {
      label: 'Admin',
      icon: 'admin',
      roles: ['Admin'],
      children: [
        { label: 'User Management', icon: 'users', route: '/admin/users' },
        { label: 'Role Management', icon: 'shield', route: '/admin/roles' },
        { label: 'System Settings', icon: 'settings', route: '/admin/settings' },
        { label: 'Integrations', icon: 'link', route: '/admin/integrations' }
      ]
    }
  ];

  expandedItems: Set<string> = new Set();

  toggleExpanded(item: NavigationItem) {
    if (item.children && !this.collapsed) {
      if (this.expandedItems.has(item.label)) {
        this.expandedItems.delete(item.label);
      } else {
        this.expandedItems.add(item.label);
      }
    }
  }

  isExpanded(item: NavigationItem): boolean {
    return this.expandedItems.has(item.label);
  }

  isItemVisible(item: NavigationItem): boolean {
    if (!item.roles || item.roles.length === 0) {
      return true;
    }
    return item.roles.includes(this.currentUserRole);
  }

  isActive(route: string): boolean {
    return this.router.url === route;
  }

  isParentActive(item: NavigationItem): boolean {
    if (!item.children) return false;
    return item.children.some(child => child.route && this.isActive(child.route));
  }

  getIconSvg(iconName: string): string {
    const icons: { [key: string]: string } = {
      dashboard: `<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
        <rect x="3" y="3" width="7" height="7" rx="1"></rect>
        <rect x="14" y="3" width="7" height="7" rx="1"></rect>
        <rect x="14" y="14" width="7" height="7" rx="1"></rect>
        <rect x="3" y="14" width="7" height="7" rx="1"></rect>
      </svg>`,
      projects: `<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
        <path d="M4 4h5l2 2h9a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1z"></path>
      </svg>`,
      bugs: `<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
        <circle cx="12" cy="12" r="8"></circle>
        <path d="M8 12h8M12 8v8" stroke="white" stroke-width="2"></path>
      </svg>`,
      reports: `<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
        <rect x="3" y="3" width="18" height="18" rx="2"></rect>
        <path d="M8 12l2 2 4-4" stroke="white" stroke-width="2" fill="none"></path>
      </svg>`,
      admin: `<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
        <rect x="4" y="4" width="16" height="16" rx="2"></rect>
        <circle cx="12" cy="10" r="2" fill="white"></circle>
        <path d="M8 16h8" stroke="white" stroke-width="2"></path>
      </svg>`,
      list: `<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
        <rect x="3" y="5" width="18" height="2" rx="1"></rect>
        <rect x="3" y="11" width="18" height="2" rx="1"></rect>
        <rect x="3" y="17" width="18" height="2" rx="1"></rect>
      </svg>`,
      plus: `<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
        <rect x="11" y="4" width="2" height="16" rx="1"></rect>
        <rect x="4" y="11" width="16" height="2" rx="1"></rect>
      </svg>`,
      user: `<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
        <circle cx="12" cy="8" r="4"></circle>
        <path d="M6 20c0-4 2.7-6 6-6s6 2 6 6"></path>
      </svg>`,
      assign: `<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
        <circle cx="9" cy="8" r="3"></circle>
        <path d="M3 20c0-3 2.7-5 6-5s6 2 6 5"></path>
        <circle cx="18" cy="8" r="2"></circle>
        <path d="M16 20c0-2 1.3-3 3-3s3 1 3 3"></path>
      </svg>`,
      chart: `<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
        <rect x="3" y="3" width="18" height="18" rx="2"></rect>
        <path d="M8 15l2-3 3 2 3-4" stroke="white" stroke-width="2" fill="none"></path>
      </svg>`,
      team: `<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
        <circle cx="8" cy="8" r="3"></circle>
        <circle cx="16" cy="8" r="3"></circle>
        <path d="M2 20c0-3 2.7-5 6-5s6 2 6 5"></path>
        <path d="M10 20c0-3 2.7-5 6-5s6 2 6 5"></path>
      </svg>`,
      users: `<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
        <circle cx="8" cy="8" r="3"></circle>
        <circle cx="16" cy="8" r="3"></circle>
        <path d="M2 20c0-3 2.7-5 6-5s6 2 6 5"></path>
        <path d="M10 20c0-3 2.7-5 6-5s6 2 6 5"></path>
      </svg>`,
      shield: `<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
        <path d="M12 2L4 6v6c0 5.5 3.8 10.7 8 12 4.2-1.3 8-6.5 8-12V6l-8-4z"></path>
        <path d="M9 12l2 2 4-4" stroke="white" stroke-width="2" fill="none"></path>
      </svg>`,
      settings: `<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
        <circle cx="12" cy="12" r="10"></circle>
        <circle cx="12" cy="12" r="3" fill="white"></circle>
        <rect x="11" y="2" width="2" height="4" rx="1"></rect>
        <rect x="11" y="18" width="2" height="4" rx="1"></rect>
        <rect x="2" y="11" width="4" height="2" rx="1"></rect>
        <rect x="18" y="11" width="4" height="2" rx="1"></rect>
      </svg>`,
      link: `<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
        <rect x="3" y="10" width="8" height="4" rx="2"></rect>
        <rect x="13" y="10" width="8" height="4" rx="2"></rect>
        <rect x="9" y="11" width="6" height="2" rx="1"></rect>
      </svg>`
    };
    return icons[iconName] || '';
  }
}
