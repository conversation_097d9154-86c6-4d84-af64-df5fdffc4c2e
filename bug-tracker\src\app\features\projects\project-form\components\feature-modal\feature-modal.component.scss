.form-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.features-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--spacing-4);
  padding-bottom: var(--spacing-4);
  border-bottom: 1px solid var(--color-gray-200);
}

.features-description {
  margin: 0;
  color: var(--color-gray-600);
  font-size: var(--font-size-sm);
  flex: 1;
}

.btn-sm {
  padding: var(--spacing-1) var(--spacing-3);
  font-size: var(--font-size-xs);
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-6);
  max-height: 400px;
  overflow-y: auto;
}

.feature-item {
  background: var(--color-gray-50);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-4);
}

.feature-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-4);
}

.feature-header h4 {
  margin: 0;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-800);
}

.btn-icon {
  background: none;
  border: none;
  padding: var(--spacing-1);
  cursor: pointer;
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover:not(:disabled) {
    background: var(--color-gray-200);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &--danger {
    color: var(--color-error-600);

    &:hover:not(:disabled) {
      background: var(--color-error-50);
      color: var(--color-error-700);
    }
  }
}

.feature-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.form-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
}

.form-label.required::after {
  content: ' *';
  color: var(--color-error-500);
}

.form-control {
  padding: var(--spacing-3);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);
  font-family: inherit;
  background: var(--color-white);

  &:focus {
    outline: none;
    border-color: var(--color-primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  &.error {
    border-color: var(--color-error-500);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
  }

  &::placeholder {
    color: var(--color-gray-400);
  }
}

textarea.form-control {
  resize: vertical;
  min-height: 80px;
}

.form-error {
  font-size: var(--font-size-xs);
  color: var(--color-error-600);
  margin-top: var(--spacing-1);

  span {
    display: block;
  }
}

.modal-footer-actions {
  display: flex;
  gap: var(--spacing-3);
  justify-content: flex-end;
}

// Button styles
.btn {
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  border: 1px solid transparent;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  gap: var(--spacing-2);

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.btn-outline {
  background: var(--color-white);
  color: var(--color-gray-700);
  border-color: var(--color-gray-300);

  &:hover:not(:disabled) {
    background: var(--color-gray-50);
    border-color: var(--color-gray-400);
  }
}

.btn-primary {
  background: var(--color-primary-600);
  color: var(--color-white);
  border-color: var(--color-primary-600);

  &:hover:not(:disabled) {
    background: var(--color-primary-700);
    border-color: var(--color-primary-700);
  }
}

// Scrollbar styling
.features-list::-webkit-scrollbar {
  width: 6px;
}

.features-list::-webkit-scrollbar-track {
  background: var(--color-gray-100);
  border-radius: 3px;
}

.features-list::-webkit-scrollbar-thumb {
  background: var(--color-gray-300);
  border-radius: 3px;
}

.features-list::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray-400);
}

// Responsive design
@media (max-width: 768px) {
  .features-header {
    flex-direction: column;
    align-items: stretch;
  }

  .modal-footer-actions {
    flex-direction: column-reverse;
    gap: var(--spacing-2);
  }

  .btn {
    width: 100%;
  }

  .features-list {
    max-height: 300px;
  }
}
