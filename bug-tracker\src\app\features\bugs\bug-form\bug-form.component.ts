import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { BugService } from '../../../core/services/bug.service';
import { ProjectService } from '../../../core/services/project.service';
import { UserService } from '../../../core/services/user.service';
import { AuthService } from '../../../core/services/auth.service';
import {
  Bug,
  CreateBugRequest,
  UpdateBugRequest,
  BugStatus,
  BugSeverity,
  BugPriority
} from '../../../core/models/bug.model';
import { Project, ProjectModule, ProjectFeature } from '../../../core/models/project.model';
import { User, UserRole } from '../../../core/models/user.model';

@Component({
  selector: 'app-bug-form',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  template: `
    <div class="bug-form">
      <!-- Header -->
      <div class="bug-form__header">
        <div class="bug-form__title-section">
          <h1 class="bug-form__title">{{ isEditMode ? 'Edit Bug' : 'Report New Bug' }}</h1>
          <p class="bug-form__subtitle">{{ isEditMode ? 'Update bug details and status' : 'Provide detailed information about the issue' }}</p>
        </div>
        <div class="bug-form__actions">
          <button type="button" class="btn btn-secondary" (click)="onCancel()">
            Cancel
          </button>
        </div>
      </div>

      <!-- Loading State -->
      <div *ngIf="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <p>{{ isEditMode ? 'Loading bug details...' : 'Loading form data...' }}</p>
      </div>

      <!-- Form -->
      <form *ngIf="!loading" [formGroup]="bugForm" (ngSubmit)="onSubmit()" class="bug-form__form">
        <div class="form-grid">
          <!-- Basic Information Section -->
          <div class="form-section">
            <h2 class="form-section__title">Basic Information</h2>

            <div class="form-row">
              <div class="form-group">
                <label for="title" class="form-label required">Bug Title</label>
                <input
                  id="title"
                  type="text"
                  class="form-input"
                  formControlName="title"
                  placeholder="Enter a clear, descriptive title"
                  [class.error]="isFieldInvalid('title')"
                />
                <div *ngIf="isFieldInvalid('title')" class="form-error">
                  <span *ngIf="bugForm.get('title')?.errors?.['required']">Title is required</span>
                  <span *ngIf="bugForm.get('title')?.errors?.['minlength']">Title must be at least 5 characters</span>
                  <span *ngIf="bugForm.get('title')?.errors?.['maxlength']">Title cannot exceed 200 characters</span>
                </div>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="description" class="form-label required">Description</label>
                <textarea
                  id="description"
                  class="form-textarea"
                  formControlName="description"
                  rows="4"
                  placeholder="Describe the bug in detail..."
                  [class.error]="isFieldInvalid('description')"
                ></textarea>
                <div *ngIf="isFieldInvalid('description')" class="form-error">
                  <span *ngIf="bugForm.get('description')?.errors?.['required']">Description is required</span>
                  <span *ngIf="bugForm.get('description')?.errors?.['minlength']">Description must be at least 10 characters</span>
                </div>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="stepsToReproduce" class="form-label required">Steps to Reproduce</label>
                <textarea
                  id="stepsToReproduce"
                  class="form-textarea"
                  formControlName="stepsToReproduce"
                  rows="4"
                  placeholder="1. Navigate to...&#10;2. Click on...&#10;3. Observe..."
                  [class.error]="isFieldInvalid('stepsToReproduce')"
                ></textarea>
                <div *ngIf="isFieldInvalid('stepsToReproduce')" class="form-error">
                  <span *ngIf="bugForm.get('stepsToReproduce')?.errors?.['required']">Steps to reproduce are required</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Project Information Section -->
          <div class="form-section">
            <h2 class="form-section__title">Project Information</h2>

            <div class="form-row">
              <div class="form-group">
                <label for="projectId" class="form-label required">Project</label>
                <select
                  id="projectId"
                  class="form-select"
                  formControlName="projectId"
                  (change)="onProjectChange()"
                  [class.error]="isFieldInvalid('projectId')"
                >
                  <option value="">Select a project</option>
                  <option *ngFor="let project of projects" [value]="project.id">{{ project.name }}</option>
                </select>
                <div *ngIf="isFieldInvalid('projectId')" class="form-error">
                  <span *ngIf="bugForm.get('projectId')?.errors?.['required']">Project is required</span>
                </div>
              </div>
            </div>

            <div class="form-row" *ngIf="selectedProject">
              <div class="form-group">
                <label for="moduleId" class="form-label required">Module</label>
                <select
                  id="moduleId"
                  class="form-select"
                  formControlName="moduleId"
                  (change)="onModuleChange()"
                  [class.error]="isFieldInvalid('moduleId')"
                >
                  <option value="">Select a module</option>
                  <option *ngFor="let module of selectedProject.modules" [value]="module.id">{{ module.name }}</option>
                </select>
                <div *ngIf="isFieldInvalid('moduleId')" class="form-error">
                  <span *ngIf="bugForm.get('moduleId')?.errors?.['required']">Module is required</span>
                </div>
              </div>
            </div>

            <div class="form-row" *ngIf="selectedModule">
              <div class="form-group">
                <label for="featureId" class="form-label required">Feature</label>
                <select
                  id="featureId"
                  class="form-select"
                  formControlName="featureId"
                  [class.error]="isFieldInvalid('featureId')"
                >
                  <option value="">Select a feature</option>
                  <option *ngFor="let feature of selectedModule.features" [value]="feature.id">{{ feature.name }}</option>
                </select>
                <div *ngIf="isFieldInvalid('featureId')" class="form-error">
                  <span *ngIf="bugForm.get('featureId')?.errors?.['required']">Feature is required</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Classification Section -->
          <div class="form-section">
            <h2 class="form-section__title">Classification</h2>

            <div class="form-row">
              <div class="form-group">
                <label for="severity" class="form-label required">Severity</label>
                <select
                  id="severity"
                  class="form-select"
                  formControlName="severity"
                  [class.error]="isFieldInvalid('severity')"
                >
                  <option value="">Select severity</option>
                  <option *ngFor="let severity of severityOptions" [value]="severity">{{ severity }}</option>
                </select>
                <div *ngIf="isFieldInvalid('severity')" class="form-error">
                  <span *ngIf="bugForm.get('severity')?.errors?.['required']">Severity is required</span>
                </div>
              </div>

              <div class="form-group">
                <label for="priority" class="form-label required">Priority</label>
                <select
                  id="priority"
                  class="form-select"
                  formControlName="priority"
                  [class.error]="isFieldInvalid('priority')"
                >
                  <option value="">Select priority</option>
                  <option *ngFor="let priority of priorityOptions" [value]="priority">{{ priority }}</option>
                </select>
                <div *ngIf="isFieldInvalid('priority')" class="form-error">
                  <span *ngIf="bugForm.get('priority')?.errors?.['required']">Priority is required</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Assignment Section (Edit Mode Only) -->
        <div class="form-section" *ngIf="isEditMode">
          <h2 class="form-section__title">Assignment & Status</h2>

          <div class="form-row">
            <div class="form-group">
              <label for="status" class="form-label">Status</label>
              <select
                id="status"
                class="form-select"
                formControlName="status"
              >
                <option *ngFor="let status of statusOptions" [value]="status">{{ status }}</option>
              </select>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="qaAssignedId" class="form-label">QA Assigned</label>
              <select
                id="qaAssignedId"
                class="form-select"
                formControlName="qaAssignedId"
              >
                <option value="">Unassigned</option>
                <option *ngFor="let user of qaUsers" [value]="user.id">{{ user.fullName }}</option>
              </select>
            </div>

            <div class="form-group">
              <label for="devAssignedId" class="form-label">Developer Assigned</label>
              <select
                id="devAssignedId"
                class="form-select"
                formControlName="devAssignedId"
              >
                <option value="">Unassigned</option>
                <option *ngFor="let user of developers" [value]="user.id">{{ user.fullName }}</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Dates Section -->
        <div class="form-section">
          <h2 class="form-section__title">Timeline</h2>

          <div class="form-row">
            <div class="form-group">
              <label for="expectedReviewDate" class="form-label">Expected Review Date</label>
              <input
                id="expectedReviewDate"
                type="date"
                class="form-input"
                formControlName="expectedReviewDate"
              />
            </div>

            <div class="form-group">
              <label for="expectedFixDate" class="form-label">Expected Fix Date</label>
              <input
                id="expectedFixDate"
                type="date"
                class="form-input"
                formControlName="expectedFixDate"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="estimatedHours" class="form-label">Estimated Hours</label>
              <input
                id="estimatedHours"
                type="number"
                class="form-input"
                formControlName="estimatedHours"
                min="0"
                step="0.5"
                placeholder="0.0"
              />
            </div>
          </div>
        </div>

        <!-- Additional Information Section -->
        <div class="form-section">
          <h2 class="form-section__title">Additional Information</h2>

          <div class="form-row">
            <div class="form-group">
              <label for="tags" class="form-label">Tags</label>
              <input
                id="tags"
                type="text"
                class="form-input"
                formControlName="tags"
                placeholder="Enter tags separated by commas (e.g., ui, performance, critical)"
              />
              <div class="form-help">Separate multiple tags with commas</div>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
          <button type="button" class="btn btn-secondary" (click)="onCancel()">
            Cancel
          </button>
          <button
            type="submit"
            class="btn btn-primary"
            [disabled]="bugForm.invalid || submitting"
          >
            <span *ngIf="submitting" class="btn-spinner"></span>
            {{ isEditMode ? 'Update Bug' : 'Create Bug' }}
          </button>
        </div>
      </form>

      <!-- Error Message -->
      <div *ngIf="errorMessage" class="error-banner">
        <div class="error-banner__content">
          <svg class="error-banner__icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="10"/>
            <line x1="15" y1="9" x2="9" y2="15"/>
            <line x1="9" y1="9" x2="15" y2="15"/>
          </svg>
          <span>{{ errorMessage }}</span>
        </div>
        <button class="error-banner__close" (click)="errorMessage = ''">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"/>
            <line x1="6" y1="6" x2="18" y2="18"/>
          </svg>
        </button>
      </div>
    </div>
  `,
  styles: [`
    .bug-form {
      max-width: 1200px;
      margin: 0 auto;
      padding: var(--spacing-6);
    }

    .bug-form__header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: var(--spacing-8);
      gap: var(--spacing-4);
    }

    .bug-form__title-section {
      flex: 1;
    }

    .bug-form__title {
      font-size: var(--font-size-3xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-gray-900);
      margin: 0 0 var(--spacing-2) 0;
    }

    .bug-form__subtitle {
      font-size: var(--font-size-lg);
      color: var(--color-gray-600);
      margin: 0;
    }

    .bug-form__actions {
      display: flex;
      gap: var(--spacing-3);
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: var(--spacing-12);
      color: var(--color-gray-600);
    }

    .loading-spinner {
      width: 32px;
      height: 32px;
      border: 3px solid var(--color-gray-200);
      border-top: 3px solid var(--color-primary-500);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: var(--spacing-3);
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .bug-form__form {
      background: var(--color-white);
      border: 1px solid var(--color-gray-200);
      border-radius: var(--border-radius-lg);
      overflow: hidden;
    }

    .form-grid {
      display: flex;
      flex-direction: column;
      gap: 0;
    }

    .form-section {
      padding: var(--spacing-6);
      border-bottom: 1px solid var(--color-gray-200);
    }

    .form-section:last-child {
      border-bottom: none;
    }

    .form-section__title {
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-900);
      margin: 0 0 var(--spacing-4) 0;
      padding-bottom: var(--spacing-2);
      border-bottom: 2px solid var(--color-primary-100);
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr;
      gap: var(--spacing-4);
      margin-bottom: var(--spacing-4);
    }

    .form-row:last-child {
      margin-bottom: 0;
    }

    @media (min-width: 768px) {
      .form-row {
        grid-template-columns: 1fr 1fr;
      }

      .form-row .form-group:only-child {
        grid-column: 1 / -1;
      }
    }

    .form-group {
      display: flex;
      flex-direction: column;
    }

    .form-label {
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-700);
      margin-bottom: var(--spacing-1);
    }

    .form-label.required::after {
      content: ' *';
      color: var(--color-red-500);
    }

    .form-input,
    .form-select,
    .form-textarea {
      padding: var(--spacing-3);
      border: 1px solid var(--color-gray-300);
      border-radius: var(--border-radius-md);
      font-size: var(--font-size-sm);
      background: var(--color-white);
      transition: border-color 0.2s ease, box-shadow 0.2s ease;
    }

    .form-input:focus,
    .form-select:focus,
    .form-textarea:focus {
      outline: none;
      border-color: var(--color-primary-500);
      box-shadow: 0 0 0 3px var(--color-primary-100);
    }

    .form-input.error,
    .form-select.error,
    .form-textarea.error {
      border-color: var(--color-red-500);
    }

    .form-input.error:focus,
    .form-select.error:focus,
    .form-textarea.error:focus {
      box-shadow: 0 0 0 3px var(--color-red-100);
    }

    .form-textarea {
      resize: vertical;
      min-height: 100px;
    }

    .form-help {
      font-size: var(--font-size-xs);
      color: var(--color-gray-500);
      margin-top: var(--spacing-1);
    }

    .form-error {
      font-size: var(--font-size-xs);
      color: var(--color-red-600);
      margin-top: var(--spacing-1);
    }

    .form-actions {
      padding: var(--spacing-6);
      background: var(--color-gray-50);
      border-top: 1px solid var(--color-gray-200);
      display: flex;
      justify-content: flex-end;
      gap: var(--spacing-3);
    }

    .btn-spinner {
      display: inline-block;
      width: 16px;
      height: 16px;
      border: 2px solid transparent;
      border-top: 2px solid currentColor;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: var(--spacing-2);
    }

    .error-banner {
      position: fixed;
      top: var(--spacing-4);
      right: var(--spacing-4);
      background: var(--color-red-50);
      border: 1px solid var(--color-red-200);
      border-radius: var(--border-radius-md);
      padding: var(--spacing-4);
      display: flex;
      align-items: center;
      gap: var(--spacing-3);
      max-width: 400px;
      z-index: 1000;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .error-banner__content {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      flex: 1;
    }

    .error-banner__icon {
      color: var(--color-red-500);
      flex-shrink: 0;
    }

    .error-banner__close {
      background: none;
      border: none;
      color: var(--color-red-500);
      cursor: pointer;
      padding: var(--spacing-1);
      border-radius: var(--border-radius-sm);
      transition: background-color 0.2s ease;
    }

    .error-banner__close:hover {
      background: var(--color-red-100);
    }

    @media (max-width: 768px) {
      .bug-form {
        padding: var(--spacing-4);
      }

      .bug-form__header {
        flex-direction: column;
        align-items: stretch;
      }

      .form-section {
        padding: var(--spacing-4);
      }

      .form-actions {
        padding: var(--spacing-4);
        flex-direction: column;
      }

      .error-banner {
        position: relative;
        top: auto;
        right: auto;
        margin-bottom: var(--spacing-4);
        max-width: none;
      }
    }
  `]
})
export class BugFormComponent implements OnInit {
  private fb = inject(FormBuilder);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private bugService = inject(BugService);
  private projectService = inject(ProjectService);
  private userService = inject(UserService);
  private authService = inject(AuthService);

  // Form
  bugForm!: FormGroup;

  // Data
  projects: Project[] = [];
  selectedProject: Project | null = null;
  selectedModule: ProjectModule | null = null;
  developers: User[] = [];
  qaUsers: User[] = [];

  // Options
  severityOptions = Object.values(BugSeverity);
  priorityOptions = Object.values(BugPriority);
  statusOptions = Object.values(BugStatus);

  // State
  loading = false;
  submitting = false;
  isEditMode = false;
  bugId: string | null = null;
  currentBug: Bug | null = null;
  errorMessage = '';

  ngOnInit() {
    this.initializeForm();
    this.loadInitialData();
    this.checkEditMode();
  }

  private initializeForm() {
    this.bugForm = this.fb.group({
      title: ['', [Validators.required, Validators.minLength(5), Validators.maxLength(200)]],
      description: ['', [Validators.required, Validators.minLength(10)]],
      stepsToReproduce: ['', [Validators.required]],
      projectId: ['', [Validators.required]],
      moduleId: ['', [Validators.required]],
      featureId: ['', [Validators.required]],
      severity: ['', [Validators.required]],
      priority: ['', [Validators.required]],
      status: [BugStatus.NEW],
      qaAssignedId: [''],
      devAssignedId: [''],
      expectedReviewDate: [''],
      expectedFixDate: [''],
      estimatedHours: [''],
      tags: ['']
    });
  }

  private loadInitialData() {
    this.loading = true;

    // Load projects
    this.projects = this.projectService.getAllProjects();

    // Load users by role
    this.userService.getUsersByRole(UserRole.DEVELOPER).subscribe(users => {
      this.developers = users;
    });

    this.userService.getUsersByRole(UserRole.QA_ENGINEER).subscribe(users => {
      this.qaUsers = users;
      this.loading = false;
    });
  }

  private checkEditMode() {
    this.bugId = this.route.snapshot.paramMap.get('id');
    this.isEditMode = !!this.bugId;

    if (this.isEditMode && this.bugId) {
      this.loadBugForEdit(this.bugId);
    }
  }

  private loadBugForEdit(id: string) {
    this.loading = true;
    this.bugService.getBugById(id).subscribe({
      next: (bug) => {
        if (bug) {
          this.currentBug = bug;
          this.populateForm(bug);
        } else {
          this.errorMessage = 'Bug not found';
          this.router.navigate(['/bugs']);
        }
        this.loading = false;
      },
      error: (error) => {
        this.errorMessage = 'Failed to load bug details';
        this.loading = false;
      }
    });
  }

  private populateForm(bug: Bug) {
    // Set project first to trigger cascading dropdowns
    this.bugForm.patchValue({
      projectId: bug.projectId
    });
    this.onProjectChange();

    // Set module to trigger feature dropdown
    this.bugForm.patchValue({
      moduleId: bug.moduleId
    });
    this.onModuleChange();

    // Set all other values
    this.bugForm.patchValue({
      title: bug.title,
      description: bug.description,
      stepsToReproduce: bug.stepsToReproduce,
      featureId: bug.featureId,
      severity: bug.severity,
      priority: bug.priority,
      status: bug.status,
      qaAssignedId: bug.qaAssignedId || '',
      devAssignedId: bug.devAssignedId || '',
      expectedReviewDate: bug.expectedReviewDate ? this.formatDateForInput(bug.expectedReviewDate) : '',
      expectedFixDate: bug.expectedFixDate ? this.formatDateForInput(bug.expectedFixDate) : '',
      estimatedHours: bug.estimatedHours || '',
      tags: bug.tags.join(', ')
    });
  }

  private formatDateForInput(date: Date): string {
    return new Date(date).toISOString().split('T')[0];
  }

  // Event handlers
  onProjectChange() {
    const projectId = this.bugForm.get('projectId')?.value;
    this.selectedProject = this.projects.find(p => p.id === projectId) || null;
    this.selectedModule = null;

    // Reset dependent fields
    this.bugForm.patchValue({
      moduleId: '',
      featureId: ''
    });
  }

  onModuleChange() {
    const moduleId = this.bugForm.get('moduleId')?.value;
    this.selectedModule = this.selectedProject?.modules.find(m => m.id === moduleId) || null;

    // Reset dependent fields
    this.bugForm.patchValue({
      featureId: ''
    });
  }

  onSubmit() {
    if (this.bugForm.invalid) {
      this.markFormGroupTouched(this.bugForm);
      return;
    }

    this.submitting = true;
    this.errorMessage = '';

    const formValue = this.bugForm.value;

    if (this.isEditMode && this.bugId) {
      this.updateBug(formValue);
    } else {
      this.createBug(formValue);
    }
  }

  private createBug(formValue: any) {
    const request: CreateBugRequest = {
      title: formValue.title,
      description: formValue.description,
      stepsToReproduce: formValue.stepsToReproduce,
      projectId: formValue.projectId,
      moduleId: formValue.moduleId,
      featureId: formValue.featureId,
      severity: formValue.severity,
      priority: formValue.priority,
      qaAssignedId: formValue.qaAssignedId || undefined,
      devAssignedId: formValue.devAssignedId || undefined,
      expectedReviewDate: formValue.expectedReviewDate ? new Date(formValue.expectedReviewDate) : undefined,
      expectedFixDate: formValue.expectedFixDate ? new Date(formValue.expectedFixDate) : undefined,
      estimatedHours: formValue.estimatedHours || undefined,
      tags: this.parseTags(formValue.tags)
    };

    this.bugService.createBug(request).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.router.navigate(['/bugs', response.data.id]);
        } else {
          this.errorMessage = response.message || 'Failed to create bug';
        }
        this.submitting = false;
      },
      error: (error) => {
        this.errorMessage = error.message || 'Failed to create bug';
        this.submitting = false;
      }
    });
  }

  private updateBug(formValue: any) {
    if (!this.bugId) return;

    const request: UpdateBugRequest = {
      title: formValue.title,
      description: formValue.description,
      stepsToReproduce: formValue.stepsToReproduce,
      severity: formValue.severity,
      priority: formValue.priority,
      status: formValue.status,
      qaAssignedId: formValue.qaAssignedId || undefined,
      devAssignedId: formValue.devAssignedId || undefined,
      expectedReviewDate: formValue.expectedReviewDate ? new Date(formValue.expectedReviewDate) : undefined,
      expectedFixDate: formValue.expectedFixDate ? new Date(formValue.expectedFixDate) : undefined,
      estimatedHours: formValue.estimatedHours || undefined,
      tags: this.parseTags(formValue.tags)
    };

    this.bugService.updateBug(this.bugId, request).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.router.navigate(['/bugs', this.bugId]);
        } else {
          this.errorMessage = response.message || 'Failed to update bug';
        }
        this.submitting = false;
      },
      error: (error) => {
        this.errorMessage = error.message || 'Failed to update bug';
        this.submitting = false;
      }
    });
  }

  onCancel() {
    if (this.isEditMode && this.bugId) {
      this.router.navigate(['/bugs', this.bugId]);
    } else {
      this.router.navigate(['/bugs']);
    }
  }

  // Utility methods
  isFieldInvalid(fieldName: string): boolean {
    const field = this.bugForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  private markFormGroupTouched(formGroup: FormGroup) {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }

  private parseTags(tagsString: string): string[] {
    if (!tagsString) return [];
    return tagsString.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
  }
}
