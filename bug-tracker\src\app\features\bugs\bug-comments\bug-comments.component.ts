import { Component, Input, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { BugService } from '../../../core/services/bug.service';
import { UserService } from '../../../core/services/user.service';
import { AuthService } from '../../../core/services/auth.service';
import { BugComment } from '../../../core/models/bug.model';
import { User, AuthUser, getUserInitials } from '../../../core/models/user.model';

@Component({
  selector: 'app-bug-comments',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  template: `
    <div class="bug-comments">
      <!-- Comments Header -->
      <div class="comments-header">
        <h3 class="comments-title">
          Comments
          <span class="comments-count" *ngIf="comments.length > 0">({{ comments.length }})</span>
        </h3>
      </div>

      <!-- Add Comment Form -->
      <div class="add-comment-section" *ngIf="canAddComment()">
        <form [formGroup]="commentForm" (ngSubmit)="onSubmitComment()" class="comment-form">
          <div class="comment-form__header">
            <div class="user-avatar">
              <div class="avatar-circle">{{ getCurrentUserInitials() }}</div>
            </div>
            <div class="form-meta">
              <span class="form-author">{{ currentUser?.fullName }}</span>
              <div class="form-options">
                <label class="checkbox-label">
                  <input 
                    type="checkbox" 
                    formControlName="isInternal"
                    class="checkbox-input"
                  />
                  <span class="checkbox-text">Internal comment</span>
                </label>
              </div>
            </div>
          </div>
          
          <div class="comment-form__body">
            <textarea
              formControlName="content"
              class="comment-textarea"
              placeholder="Add a comment... Use @username to mention someone"
              rows="4"
              [class.error]="isFieldInvalid('content')"
            ></textarea>
            <div *ngIf="isFieldInvalid('content')" class="form-error">
              <span *ngIf="commentForm.get('content')?.errors?.['required']">Comment is required</span>
              <span *ngIf="commentForm.get('content')?.errors?.['minlength']">Comment must be at least 3 characters</span>
            </div>
          </div>

          <!-- Mention Suggestions -->
          <div class="mention-suggestions" *ngIf="showMentionSuggestions">
            <div class="suggestions-list">
              <div 
                class="suggestion-item"
                *ngFor="let user of mentionSuggestions"
                (click)="selectMention(user)"
              >
                <div class="suggestion-avatar">
                  <div class="avatar-circle small">{{ getUserInitials(user) }}</div>
                </div>
                <div class="suggestion-info">
                  <span class="suggestion-name">{{ user.fullName }}</span>
                  <span class="suggestion-role">{{ user.role }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="comment-form__actions">
            <button 
              type="button" 
              class="btn btn-secondary btn-sm"
              (click)="cancelComment()"
              *ngIf="commentForm.get('content')?.value"
            >
              Cancel
            </button>
            <button 
              type="submit" 
              class="btn btn-primary btn-sm"
              [disabled]="commentForm.invalid || submitting"
            >
              <span *ngIf="submitting" class="btn-spinner"></span>
              Add Comment
            </button>
          </div>
        </form>
      </div>

      <!-- Comments List -->
      <div class="comments-list" *ngIf="comments.length > 0">
        <div 
          class="comment-item"
          *ngFor="let comment of comments; trackBy: trackByCommentId"
          [class.internal-comment]="comment.isInternal"
        >
          <div class="comment-avatar">
            <div class="avatar-circle">{{ getUserInitials(comment.author) }}</div>
          </div>
          
          <div class="comment-content">
            <div class="comment-header">
              <div class="comment-meta">
                <span class="comment-author">{{ comment.author.fullName }}</span>
                <span class="comment-role">{{ comment.author.role }}</span>
                <span class="comment-time">{{ formatCommentTime(comment.createdAt) }}</span>
                <span class="internal-badge" *ngIf="comment.isInternal">Internal</span>
              </div>
              <div class="comment-actions" *ngIf="canEditComment(comment)">
                <button 
                  class="action-btn"
                  (click)="editComment(comment)"
                  title="Edit comment"
                >
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                  </svg>
                </button>
                <button 
                  class="action-btn delete"
                  (click)="deleteComment(comment)"
                  title="Delete comment"
                >
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="3,6 5,6 21,6"/>
                    <path d="M19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"/>
                  </svg>
                </button>
              </div>
            </div>
            
            <div class="comment-body">
              <div 
                class="comment-text"
                [innerHTML]="formatCommentContent(comment.content)"
              ></div>
              
              <!-- Mentioned Users -->
              <div class="mentioned-users" *ngIf="comment.mentionedUsers.length > 0">
                <span class="mentions-label">Mentioned:</span>
                <span 
                  class="mention-tag"
                  *ngFor="let user of comment.mentionedUsers"
                >
                  {{ user.fullName }}
                </span>
              </div>
              
              <!-- Comment Attachments -->
              <div class="comment-attachments" *ngIf="comment.attachments.length > 0">
                <div class="attachments-label">Attachments:</div>
                <div class="attachments-list">
                  <div 
                    class="attachment-item"
                    *ngFor="let attachment of comment.attachments"
                  >
                    <svg class="attachment-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <path d="M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66l-9.2 9.19a2 2 0 0 1-2.83-2.83l8.49-8.48"/>
                    </svg>
                    <a [href]="attachment.downloadUrl" class="attachment-link" target="_blank">
                      {{ attachment.originalFileName }}
                    </a>
                    <span class="attachment-size">({{ formatFileSize(attachment.fileSize) }})</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="comment-updated" *ngIf="comment.updatedAt > comment.createdAt">
              <span class="updated-text">Edited {{ formatCommentTime(comment.updatedAt) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div class="comments-empty" *ngIf="comments.length === 0">
        <div class="empty-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
          </svg>
        </div>
        <h4 class="empty-title">No comments yet</h4>
        <p class="empty-description">Be the first to add a comment to this bug report.</p>
      </div>

      <!-- Loading State -->
      <div class="comments-loading" *ngIf="loading">
        <div class="loading-spinner"></div>
        <span>Loading comments...</span>
      </div>

      <!-- Error State -->
      <div class="comments-error" *ngIf="errorMessage">
        <div class="error-content">
          <svg class="error-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="10"/>
            <line x1="15" y1="9" x2="9" y2="15"/>
            <line x1="9" y1="9" x2="15" y2="15"/>
          </svg>
          <span>{{ errorMessage }}</span>
        </div>
        <button class="btn btn-sm btn-secondary" (click)="retryLoadComments()">
          Try Again
        </button>
      </div>
    </div>
  `,
  styles: [`
    .bug-comments {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-6);
    }

    .comments-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: var(--spacing-3);
      border-bottom: 2px solid var(--color-primary-100);
    }

    .comments-title {
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-900);
      margin: 0;
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
    }

    .comments-count {
      font-size: var(--font-size-sm);
      color: var(--color-gray-500);
      font-weight: var(--font-weight-normal);
    }

    .add-comment-section {
      background: var(--color-white);
      border: 1px solid var(--color-gray-200);
      border-radius: var(--border-radius-lg);
      padding: var(--spacing-4);
    }

    .comment-form {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-3);
    }

    .comment-form__header {
      display: flex;
      gap: var(--spacing-3);
      align-items: flex-start;
    }

    .user-avatar {
      flex-shrink: 0;
    }

    .avatar-circle {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: var(--color-primary-500);
      color: var(--color-white);
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: var(--font-weight-medium);
      font-size: var(--font-size-sm);
    }

    .avatar-circle.small {
      width: 32px;
      height: 32px;
      font-size: var(--font-size-xs);
    }

    .form-meta {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: var(--spacing-1);
    }

    .form-author {
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-900);
    }

    .form-options {
      display: flex;
      gap: var(--spacing-4);
    }

    .checkbox-label {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      font-size: var(--font-size-sm);
      color: var(--color-gray-600);
      cursor: pointer;
    }

    .checkbox-input {
      margin: 0;
    }

    .comment-form__body {
      margin-left: 52px;
    }

    .comment-textarea {
      width: 100%;
      min-height: 100px;
      padding: var(--spacing-3);
      border: 1px solid var(--color-gray-300);
      border-radius: var(--border-radius-md);
      font-size: var(--font-size-sm);
      font-family: inherit;
      resize: vertical;
      transition: border-color 0.2s ease, box-shadow 0.2s ease;
    }

    .comment-textarea:focus {
      outline: none;
      border-color: var(--color-primary-500);
      box-shadow: 0 0 0 3px var(--color-primary-100);
    }

    .comment-textarea.error {
      border-color: var(--color-red-500);
    }

    .form-error {
      font-size: var(--font-size-xs);
      color: var(--color-red-600);
      margin-top: var(--spacing-1);
    }

    .comment-form__actions {
      margin-left: 52px;
      display: flex;
      justify-content: flex-end;
      gap: var(--spacing-2);
    }

    .btn-spinner {
      display: inline-block;
      width: 14px;
      height: 14px;
      border: 2px solid transparent;
      border-top: 2px solid currentColor;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: var(--spacing-1);
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Mention Suggestions */
    .mention-suggestions {
      position: relative;
      margin-left: 52px;
    }

    .suggestions-list {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      background: var(--color-white);
      border: 1px solid var(--color-gray-300);
      border-radius: var(--border-radius-md);
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      max-height: 200px;
      overflow-y: auto;
      z-index: 10;
    }

    .suggestion-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-3);
      padding: var(--spacing-2) var(--spacing-3);
      cursor: pointer;
      transition: background-color 0.2s ease;
    }

    .suggestion-item:hover {
      background: var(--color-gray-50);
    }

    .suggestion-avatar {
      flex-shrink: 0;
    }

    .suggestion-info {
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    .suggestion-name {
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-900);
      font-size: var(--font-size-sm);
    }

    .suggestion-role {
      font-size: var(--font-size-xs);
      color: var(--color-gray-500);
    }

    /* Comments List */
    .comments-list {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-4);
    }

    .comment-item {
      display: flex;
      gap: var(--spacing-3);
      padding: var(--spacing-4);
      background: var(--color-white);
      border: 1px solid var(--color-gray-200);
      border-radius: var(--border-radius-lg);
      position: relative;
    }

    .comment-item.internal-comment {
      background: var(--color-yellow-50);
      border-color: var(--color-yellow-200);
    }

    .comment-item.internal-comment::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 4px;
      background: var(--color-yellow-400);
      border-radius: var(--border-radius-sm) 0 0 var(--border-radius-sm);
    }

    .comment-avatar {
      flex-shrink: 0;
    }

    .comment-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: var(--spacing-2);
    }

    .comment-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
    }

    .comment-meta {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      flex-wrap: wrap;
    }

    .comment-author {
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-900);
      font-size: var(--font-size-sm);
    }

    .comment-role {
      font-size: var(--font-size-xs);
      color: var(--color-gray-500);
      background: var(--color-gray-100);
      padding: var(--spacing-1) var(--spacing-2);
      border-radius: var(--border-radius-sm);
    }

    .comment-time {
      font-size: var(--font-size-xs);
      color: var(--color-gray-500);
    }

    .internal-badge {
      font-size: var(--font-size-xs);
      background: var(--color-yellow-100);
      color: var(--color-yellow-800);
      padding: var(--spacing-1) var(--spacing-2);
      border-radius: var(--border-radius-sm);
      font-weight: var(--font-weight-medium);
    }

    .comment-actions {
      display: flex;
      gap: var(--spacing-1);
      opacity: 0;
      transition: opacity 0.2s ease;
    }

    .comment-item:hover .comment-actions {
      opacity: 1;
    }

    .action-btn {
      background: none;
      border: none;
      color: var(--color-gray-400);
      cursor: pointer;
      padding: var(--spacing-1);
      border-radius: var(--border-radius-sm);
      transition: color 0.2s ease, background-color 0.2s ease;
    }

    .action-btn:hover {
      color: var(--color-gray-600);
      background: var(--color-gray-100);
    }

    .action-btn.delete:hover {
      color: var(--color-red-600);
      background: var(--color-red-50);
    }

    .comment-body {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-3);
    }

    .comment-text {
      line-height: 1.6;
      color: var(--color-gray-700);
      white-space: pre-wrap;
    }

    .comment-text :global(.mention) {
      background: var(--color-primary-100);
      color: var(--color-primary-800);
      padding: 2px 4px;
      border-radius: var(--border-radius-sm);
      font-weight: var(--font-weight-medium);
    }

    .mentioned-users {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      flex-wrap: wrap;
    }

    .mentions-label {
      font-size: var(--font-size-xs);
      color: var(--color-gray-500);
      font-weight: var(--font-weight-medium);
    }

    .mention-tag {
      font-size: var(--font-size-xs);
      background: var(--color-primary-100);
      color: var(--color-primary-800);
      padding: var(--spacing-1) var(--spacing-2);
      border-radius: var(--border-radius-sm);
      font-weight: var(--font-weight-medium);
    }

    .comment-attachments {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-2);
    }

    .attachments-label {
      font-size: var(--font-size-xs);
      color: var(--color-gray-500);
      font-weight: var(--font-weight-medium);
    }

    .attachments-list {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-1);
    }

    .attachment-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
      padding: var(--spacing-2);
      background: var(--color-gray-50);
      border-radius: var(--border-radius-sm);
    }

    .attachment-icon {
      color: var(--color-gray-500);
      flex-shrink: 0;
    }

    .attachment-link {
      color: var(--color-primary-600);
      text-decoration: none;
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
    }

    .attachment-link:hover {
      text-decoration: underline;
    }

    .attachment-size {
      font-size: var(--font-size-xs);
      color: var(--color-gray-500);
    }

    .comment-updated {
      font-size: var(--font-size-xs);
      color: var(--color-gray-500);
      font-style: italic;
    }

    /* Empty State */
    .comments-empty {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: var(--spacing-12);
      text-align: center;
      background: var(--color-white);
      border: 1px solid var(--color-gray-200);
      border-radius: var(--border-radius-lg);
    }

    .empty-icon {
      color: var(--color-gray-300);
      margin-bottom: var(--spacing-4);
    }

    .empty-title {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-900);
      margin: 0 0 var(--spacing-2) 0;
    }

    .empty-description {
      font-size: var(--font-size-sm);
      color: var(--color-gray-500);
      margin: 0;
    }

    /* Loading State */
    .comments-loading {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--spacing-3);
      padding: var(--spacing-8);
      color: var(--color-gray-600);
    }

    .loading-spinner {
      width: 20px;
      height: 20px;
      border: 2px solid var(--color-gray-200);
      border-top: 2px solid var(--color-primary-500);
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    /* Error State */
    .comments-error {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--spacing-4);
      background: var(--color-red-50);
      border: 1px solid var(--color-red-200);
      border-radius: var(--border-radius-md);
      color: var(--color-red-800);
    }

    .error-content {
      display: flex;
      align-items: center;
      gap: var(--spacing-2);
    }

    .error-icon {
      color: var(--color-red-500);
      flex-shrink: 0;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .comment-form__header {
        flex-direction: column;
        gap: var(--spacing-2);
      }

      .comment-form__body,
      .comment-form__actions,
      .mention-suggestions {
        margin-left: 0;
      }

      .comment-item {
        flex-direction: column;
        gap: var(--spacing-2);
      }

      .comment-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-1);
      }

      .comment-actions {
        opacity: 1;
      }

      .suggestions-list {
        position: fixed;
        left: var(--spacing-4);
        right: var(--spacing-4);
        top: auto;
        bottom: var(--spacing-4);
      }
    }
  `]
})
export class BugCommentsComponent implements OnInit {
  @Input() bugId!: string;
  @Input() comments: BugComment[] = [];

  private fb = inject(FormBuilder);
  private bugService = inject(BugService);
  private userService = inject(UserService);
  private authService = inject(AuthService);

  // Form
  commentForm!: FormGroup;
  
  // State
  loading = false;
  submitting = false;
  errorMessage = '';
  
  // User data
  currentUser: AuthUser | null = null;
  allUsers: User[] = [];
  
  // Mentions
  showMentionSuggestions = false;
  mentionSuggestions: User[] = [];
  mentionQuery = '';

  ngOnInit() {
    this.currentUser = this.authService.getCurrentUser();
    this.initializeForm();
    this.loadUsers();
  }

  private initializeForm() {
    this.commentForm = this.fb.group({
      content: ['', [Validators.required, Validators.minLength(3)]],
      isInternal: [false]
    });

    // Listen for mention triggers
    this.commentForm.get('content')?.valueChanges.subscribe(value => {
      this.handleMentionInput(value);
    });
  }

  private loadUsers() {
    this.userService.getAllUsers().subscribe({
      next: (users: User[]) => {
        this.allUsers = users;
      },
      error: (error: any) => {
        console.error('Error loading users:', error);
      }
    });
  }

  // Permission methods
  canAddComment(): boolean {
    return !!this.currentUser;
  }

  canEditComment(comment: BugComment): boolean {
    if (!this.currentUser) return false;
    return comment.authorId === this.currentUser.id;
  }

  // Form methods
  onSubmitComment() {
    if (this.commentForm.invalid || !this.bugId) return;

    this.submitting = true;
    this.errorMessage = '';

    const formValue = this.commentForm.value;
    
    this.bugService.addComment(this.bugId, formValue.content, formValue.isInternal).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          // Comment added successfully - parent component should refresh
          this.commentForm.reset({ isInternal: false });
          this.showMentionSuggestions = false;
        } else {
          this.errorMessage = response.message || 'Failed to add comment';
        }
        this.submitting = false;
      },
      error: (error) => {
        this.errorMessage = error.message || 'Failed to add comment';
        this.submitting = false;
      }
    });
  }

  cancelComment() {
    this.commentForm.reset({ isInternal: false });
    this.showMentionSuggestions = false;
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.commentForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  // Mention functionality
  private handleMentionInput(value: string) {
    const mentionMatch = value.match(/@(\w*)$/);
    if (mentionMatch) {
      this.mentionQuery = mentionMatch[1].toLowerCase();
      this.mentionSuggestions = this.allUsers.filter(user => 
        user.fullName.toLowerCase().includes(this.mentionQuery) ||
        user.email.toLowerCase().includes(this.mentionQuery)
      ).slice(0, 5);
      this.showMentionSuggestions = true;
    } else {
      this.showMentionSuggestions = false;
    }
  }

  selectMention(user: User) {
    const currentValue = this.commentForm.get('content')?.value || '';
    const newValue = currentValue.replace(/@\w*$/, `@${user.fullName} `);
    this.commentForm.patchValue({ content: newValue });
    this.showMentionSuggestions = false;
  }

  // Comment actions
  editComment(comment: BugComment) {
    const newContent = prompt('Edit comment:', comment.content);
    if (newContent && newContent.trim() && newContent !== comment.content) {
      this.bugService.updateComment(this.bugId, comment.id, newContent.trim()).subscribe({
        next: (response) => {
          if (response.success) {
            // Comment updated successfully - parent component should refresh
            console.log('Comment updated successfully');
          } else {
            this.errorMessage = response.message || 'Failed to update comment';
          }
        },
        error: (error) => {
          this.errorMessage = error.message || 'Failed to update comment';
        }
      });
    }
  }

  deleteComment(comment: BugComment) {
    if (confirm('Are you sure you want to delete this comment? This action cannot be undone.')) {
      this.bugService.deleteComment(this.bugId, comment.id).subscribe({
        next: (response) => {
          if (response.success) {
            // Comment deleted successfully - parent component should refresh
            console.log('Comment deleted successfully');
          } else {
            this.errorMessage = response.message || 'Failed to delete comment';
          }
        },
        error: (error) => {
          this.errorMessage = error.message || 'Failed to delete comment';
        }
      });
    }
  }

  retryLoadComments() {
    this.errorMessage = '';
    // Parent component should handle retry
  }

  // Utility methods
  getCurrentUserInitials(): string {
    return this.currentUser ? getUserInitials(this.currentUser) : '?';
  }

  getUserInitials(user: User): string {
    return getUserInitials(user);
  }

  formatCommentTime(date: Date): string {
    const now = new Date();
    const commentDate = new Date(date);
    const diffMs = now.getTime() - commentDate.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    
    return commentDate.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: commentDate.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
    });
  }

  formatCommentContent(content: string): string {
    // Convert @mentions to highlighted spans
    return content.replace(/@(\w+(?:\s+\w+)*)/g, '<span class="mention">@$1</span>');
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  trackByCommentId(index: number, comment: BugComment): string {
    return comment.id;
  }
}
