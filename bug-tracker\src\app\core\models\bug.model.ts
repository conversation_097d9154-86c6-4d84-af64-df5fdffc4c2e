import { User } from './user.model';
import { Project, ProjectModule, ProjectFeature } from './project.model';

// Watcher-related interfaces (defined first to avoid forward reference issues)
export interface WatcherNotificationPreferences {
  onStatusChange: boolean;
  onAssignment: boolean;
  onComment: boolean;
  onAttachment: boolean;
  onPriorityChange: boolean;
  onSeverityChange: boolean;
  emailNotifications: boolean;
  inAppNotifications: boolean;
}

export interface BugWatcher {
  id: string;
  bugId: string;
  userId: string;
  user: User;
  watchedAt: Date;
  notificationPreferences: WatcherNotificationPreferences;
  addedById: string;
  addedBy: User;
}

export interface WatcherRequest {
  userId: string;
  notificationPreferences?: Partial<WatcherNotificationPreferences>;
}

export const DEFAULT_WATCHER_PREFERENCES: WatcherNotificationPreferences = {
  onStatusChange: true,
  onAssignment: true,
  onComment: true,
  onAttachment: false,
  onPriorityChange: true,
  onSeverityChange: true,
  emailNotifications: true,
  inAppNotifications: true
};

export interface Bug {
  id: string;
  title: string;
  description: string;
  stepsToReproduce: string;
  projectId: string;
  project: Project;
  moduleId: string;
  module: ProjectModule;
  featureId: string;
  feature: ProjectFeature;
  severity: BugSeverity;
  priority: BugPriority;
  status: BugStatus;
  qaAssignedId?: string;
  qaAssigned?: User;
  devAssignedId?: string;
  devAssigned?: User;
  reportedById: string;
  reportedBy: User;
  issueDate: Date;
  expectedReviewDate?: Date;
  expectedFixDate?: Date;
  actualFixDate?: Date;
  retestDate?: Date;
  closedDate?: Date;
  attachments: BugAttachment[];
  comments: BugComment[];
  auditLogs: BugAuditLog[];
  watchers: BugWatcher[];
  tags: string[];
  estimatedHours?: number;
  actualHours?: number;
  reopenCount: number;
  createdAt: Date;
  updatedAt: Date;
}

export enum BugSeverity {
  LOW = 'Low',
  MEDIUM = 'Medium',
  HIGH = 'High',
  CRITICAL = 'Critical'
}

export enum BugPriority {
  LOW = 'Low',
  MEDIUM = 'Medium',
  HIGH = 'High',
  CRITICAL = 'Critical'
}

export enum BugStatus {
  NEW = 'New',
  IN_PROGRESS = 'In Progress',
  FIXED = 'Fixed',
  READY_FOR_RETEST = 'Ready for Retest',
  CLOSED = 'Closed',
  REOPENED = 'Reopened',
  REJECTED = 'Rejected',
  DUPLICATE = 'Duplicate'
}

export interface BugAttachment {
  id: string;
  bugId: string;
  fileName: string;
  originalFileName: string;
  fileSize: number;
  mimeType: string;
  uploadedById: string;
  uploadedBy: User;
  uploadedAt: Date;
  description?: string;
  isImage: boolean;
  thumbnailUrl?: string;
  downloadUrl: string;
}

export interface BugComment {
  id: string;
  bugId: string;
  content: string;
  authorId: string;
  author: User;
  createdAt: Date;
  updatedAt: Date;
  isInternal: boolean;
  mentionedUsers: User[];
  attachments: BugAttachment[];
}

export interface BugAuditLog {
  id: string;
  bugId: string;
  action: BugAuditAction;
  field?: string;
  oldValue?: string;
  newValue?: string;
  performedById: string;
  performedBy: User;
  performedAt: Date;
  description: string;
}

export enum BugAuditAction {
  CREATED = 'Created',
  UPDATED = 'Updated',
  STATUS_CHANGED = 'Status Changed',
  ASSIGNED = 'Assigned',
  UNASSIGNED = 'Unassigned',
  COMMENTED = 'Commented',
  ATTACHMENT_ADDED = 'Attachment Added',
  ATTACHMENT_REMOVED = 'Attachment Removed',
  REOPENED = 'Reopened',
  CLOSED = 'Closed',
  WATCHER_ADDED = 'Watcher Added',
  WATCHER_REMOVED = 'Watcher Removed'
}

export interface CreateBugRequest {
  title: string;
  description: string;
  stepsToReproduce: string;
  projectId: string;
  moduleId: string;
  featureId: string;
  severity: BugSeverity;
  priority: BugPriority;
  qaAssignedId?: string;
  devAssignedId?: string;
  expectedReviewDate?: Date;
  expectedFixDate?: Date;
  tags?: string[];
  estimatedHours?: number;
  attachments?: File[];
  initialWatchers?: WatcherRequest[];
}

export interface UpdateBugRequest {
  title?: string;
  description?: string;
  stepsToReproduce?: string;
  severity?: BugSeverity;
  priority?: BugPriority;
  status?: BugStatus;
  qaAssignedId?: string;
  devAssignedId?: string;
  expectedReviewDate?: Date;
  expectedFixDate?: Date;
  actualFixDate?: Date;
  tags?: string[];
  estimatedHours?: number;
  actualHours?: number;
}

export interface BugFilter {
  projectId?: string;
  projectIds?: string[];
  moduleIds?: string[];
  featureIds?: string[];
  severity?: BugSeverity[];
  severities?: BugSeverity[];
  priority?: BugPriority[];
  priorities?: BugPriority[];
  status?: BugStatus[];
  statuses?: BugStatus[];
  assignedToId?: string;
  qaAssignedIds?: string[];
  devAssignedIds?: string[];
  reportedById?: string;
  reportedByIds?: string[];
  dateFrom?: Date;
  dateTo?: Date;
  dateRange?: {
    start: Date;
    end: Date;
  };
  search?: string;
  tags?: string[];
  hasAttachments?: boolean;
  isOverdue?: boolean;
}

export interface BugListResponse {
  bugs: Bug[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface BugSummary {
  id: string;
  title: string;
  projectName: string;
  moduleName: string;
  featureName: string;
  severity: BugSeverity;
  priority: BugPriority;
  status: BugStatus;
  qaAssigned?: string;
  devAssigned?: string;
  reportedBy: string;
  issueDate: Date;
  expectedFixDate?: Date;
  actualFixDate?: Date;
  isOverdue: boolean;
  daysSinceReported: number;
}

// Helper functions
export function getBugStatusColor(status: BugStatus): string {
  const statusColors: Record<BugStatus, string> = {
    [BugStatus.NEW]: 'info',
    [BugStatus.IN_PROGRESS]: 'warning',
    [BugStatus.FIXED]: 'success',
    [BugStatus.READY_FOR_RETEST]: 'info',
    [BugStatus.CLOSED]: 'success',
    [BugStatus.REOPENED]: 'error',
    [BugStatus.REJECTED]: 'gray',
    [BugStatus.DUPLICATE]: 'gray'
  };
  return statusColors[status];
}

export function getBugSeverityColor(severity: BugSeverity): string {
  const severityColors: Record<BugSeverity, string> = {
    [BugSeverity.LOW]: 'success',
    [BugSeverity.MEDIUM]: 'warning',
    [BugSeverity.HIGH]: 'error',
    [BugSeverity.CRITICAL]: 'error'
  };
  return severityColors[severity];
}

export function getBugPriorityColor(priority: BugPriority): string {
  const priorityColors: Record<BugPriority, string> = {
    [BugPriority.LOW]: 'gray',
    [BugPriority.MEDIUM]: 'info',
    [BugPriority.HIGH]: 'warning',
    [BugPriority.CRITICAL]: 'error'
  };
  return priorityColors[priority];
}

export function isBugOverdue(bug: Bug): boolean {
  if (!bug.expectedFixDate || bug.status === BugStatus.CLOSED) {
    return false;
  }
  return new Date() > new Date(bug.expectedFixDate);
}

export function calculateDaysSinceReported(bug: Bug): number {
  const now = new Date();
  const reported = new Date(bug.issueDate);
  const diffTime = Math.abs(now.getTime() - reported.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

export function canUserEditBug(bug: Bug, userId: string): boolean {
  return bug.reportedById === userId || 
         bug.qaAssignedId === userId || 
         bug.devAssignedId === userId;
}

export function getNextValidStatuses(currentStatus: BugStatus, userRole: string): BugStatus[] {
  const transitions: Record<BugStatus, Record<string, BugStatus[]>> = {
    [BugStatus.NEW]: {
      'Developer': [BugStatus.IN_PROGRESS, BugStatus.REJECTED],
      'QA Engineer': [BugStatus.DUPLICATE],
      'Admin': [BugStatus.IN_PROGRESS, BugStatus.REJECTED, BugStatus.DUPLICATE]
    },
    [BugStatus.IN_PROGRESS]: {
      'Developer': [BugStatus.FIXED, BugStatus.NEW],
      'Admin': [BugStatus.FIXED, BugStatus.NEW]
    },
    [BugStatus.FIXED]: {
      'QA Engineer': [BugStatus.READY_FOR_RETEST, BugStatus.CLOSED, BugStatus.REOPENED],
      'Admin': [BugStatus.READY_FOR_RETEST, BugStatus.CLOSED, BugStatus.REOPENED]
    },
    [BugStatus.READY_FOR_RETEST]: {
      'QA Engineer': [BugStatus.CLOSED, BugStatus.REOPENED],
      'Admin': [BugStatus.CLOSED, BugStatus.REOPENED]
    },
    [BugStatus.REOPENED]: {
      'Developer': [BugStatus.IN_PROGRESS],
      'Admin': [BugStatus.IN_PROGRESS]
    },
    [BugStatus.CLOSED]: {
      'QA Engineer': [BugStatus.REOPENED],
      'Admin': [BugStatus.REOPENED]
    },
    [BugStatus.REJECTED]: {},
    [BugStatus.DUPLICATE]: {}
  };

  return transitions[currentStatus]?.[userRole] || [];
}

export interface BugMetrics {
  totalBugs: number;
  openBugs: number;
  closedBugs: number;
  criticalBugs: number;
  overdueBugs: number;
  bugsByStatus: Record<BugStatus, number>;
  bugsBySeverity: Record<BugSeverity, number>;
  bugsByPriority: Record<BugPriority, number>;
  averageTimeToFix: number;
  reopenRate: number;
}
