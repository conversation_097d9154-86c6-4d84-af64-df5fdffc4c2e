.form-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.form-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
}

.form-label.required::after {
  content: ' *';
  color: var(--color-error-500);
}

.form-control {
  padding: var(--spacing-3);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);
  font-family: inherit;

  &:focus {
    outline: none;
    border-color: var(--color-primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  &.error {
    border-color: var(--color-error-500);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
  }

  &::placeholder {
    color: var(--color-gray-400);
  }
}

textarea.form-control {
  resize: vertical;
  min-height: 100px;
}

.form-error {
  font-size: var(--font-size-xs);
  color: var(--color-error-600);
  margin-top: var(--spacing-1);

  span {
    display: block;
  }
}

.modal-footer-actions {
  display: flex;
  gap: var(--spacing-3);
  justify-content: flex-end;
}

// Button styles (if not already defined globally)
.btn {
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  border: 1px solid transparent;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.btn-outline {
  background: var(--color-white);
  color: var(--color-gray-700);
  border-color: var(--color-gray-300);

  &:hover:not(:disabled) {
    background: var(--color-gray-50);
    border-color: var(--color-gray-400);
  }
}

.btn-primary {
  background: var(--color-primary-600);
  color: var(--color-white);
  border-color: var(--color-primary-600);

  &:hover:not(:disabled) {
    background: var(--color-primary-700);
    border-color: var(--color-primary-700);
  }
}

// Responsive design
@media (max-width: 768px) {
  .modal-footer-actions {
    flex-direction: column-reverse;
    gap: var(--spacing-2);
  }

  .btn {
    width: 100%;
  }
}
