import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from '../../../core/services/auth.service';
import { UserRole } from '../../../core/models/user.model';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  template: `
    <div class="login-container">
      <div class="login-card">
        <div class="login-header">
          <div class="login-logo">
            <svg width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M12 2L2 7L12 12L22 7L12 2Z"></path>
              <path d="M2 17L12 22L22 17"></path>
              <path d="M2 12L12 17L22 12"></path>
            </svg>
          </div>
          <h1>BugTracker Pro</h1>
          <p>Sign in to your account</p>
        </div>

        <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
          <div class="form-group">
            <label for="email" class="form-label">Email</label>
            <input
              type="email"
              id="email"
              formControlName="email"
              class="form-control"
              [class.is-invalid]="loginForm.get('email')?.invalid && loginForm.get('email')?.touched"
              placeholder="Enter your email">
            <div *ngIf="loginForm.get('email')?.invalid && loginForm.get('email')?.touched" class="form-error">
              <span *ngIf="loginForm.get('email')?.errors?.['required']">Email is required</span>
              <span *ngIf="loginForm.get('email')?.errors?.['email']">Please enter a valid email</span>
            </div>
          </div>

          <div class="form-group">
            <label for="password" class="form-label">Password</label>
            <input
              type="password"
              id="password"
              formControlName="password"
              class="form-control"
              [class.is-invalid]="loginForm.get('password')?.invalid && loginForm.get('password')?.touched"
              placeholder="Enter your password">
            <div *ngIf="loginForm.get('password')?.invalid && loginForm.get('password')?.touched" class="form-error">
              <span *ngIf="loginForm.get('password')?.errors?.['required']">Password is required</span>
            </div>
          </div>

          <div class="form-group">
            <label class="d-flex align-center gap-2">
              <input type="checkbox" formControlName="rememberMe" class="form-checkbox">
              <span class="text-sm text-gray-600">Remember me</span>
            </label>
          </div>

          <button 
            type="submit" 
            class="btn btn-primary w-full"
            [disabled]="loginForm.invalid || isLoading">
            <span *ngIf="isLoading" class="spinner mr-2"></span>
            {{ isLoading ? 'Signing in...' : 'Sign in' }}
          </button>

          <div *ngIf="errorMessage" class="alert alert-error mt-4">
            {{ errorMessage }}
          </div>
        </form>

        <!-- Demo Users -->
        <div class="demo-users">
          <h3>Demo Users</h3>
          <p class="text-sm text-gray-600 mb-4">Click to login as different user roles:</p>
          <div class="demo-user-buttons">
            <button 
              *ngFor="let user of demoUsers" 
              (click)="loginAsDemo(user.role)"
              class="btn btn-outline btn-sm">
              {{ user.role }}
            </button>
          </div>
          <p class="text-xs text-gray-500 mt-2">Password: password123</p>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .login-container {
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, var(--color-primary-50) 0%, var(--color-secondary-50) 100%);
      padding: var(--spacing-4);
    }

    .login-card {
      background: var(--color-white);
      border-radius: var(--border-radius-xl);
      box-shadow: var(--shadow-xl);
      padding: var(--spacing-8);
      width: 100%;
      max-width: 400px;
    }

    .login-header {
      text-align: center;
      margin-bottom: var(--spacing-8);
    }

    .login-logo {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 80px;
      height: 80px;
      background: var(--color-primary-100);
      color: var(--color-primary-600);
      border-radius: var(--border-radius-full);
      margin-bottom: var(--spacing-4);
    }

    .login-header h1 {
      font-size: var(--font-size-2xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-gray-900);
      margin-bottom: var(--spacing-2);
    }

    .login-header p {
      color: var(--color-gray-600);
      font-size: var(--font-size-sm);
    }

    .login-form {
      margin-bottom: var(--spacing-8);
    }

    .form-checkbox {
      width: 16px;
      height: 16px;
      border: 1px solid var(--color-gray-300);
      border-radius: var(--border-radius-base);
    }

    .demo-users {
      border-top: 1px solid var(--color-gray-200);
      padding-top: var(--spacing-6);
      text-align: center;
    }

    .demo-users h3 {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-900);
      margin-bottom: var(--spacing-2);
    }

    .demo-user-buttons {
      display: flex;
      flex-wrap: wrap;
      gap: var(--spacing-2);
      justify-content: center;
      margin-bottom: var(--spacing-2);
    }

    .mr-2 {
      margin-right: var(--spacing-2);
    }
  `]
})
export class LoginComponent {
  private fb = inject(FormBuilder);
  private authService = inject(AuthService);
  private router = inject(Router);

  loginForm: FormGroup;
  isLoading = false;
  errorMessage = '';

  demoUsers = [
    { role: UserRole.ADMIN },
    { role: UserRole.PROJECT_MANAGER },
    { role: UserRole.DEVELOPER },
    { role: UserRole.QA_ENGINEER },
    { role: UserRole.BUSINESS_ANALYST }
  ];

  constructor() {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required]],
      rememberMe: [false]
    });
  }

  onSubmit() {
    if (this.loginForm.valid) {
      this.isLoading = true;
      this.errorMessage = '';

      this.authService.login(this.loginForm.value).subscribe({
        next: (response) => {
          this.isLoading = false;
          this.router.navigate(['/dashboard']);
        },
        error: (error) => {
          this.isLoading = false;
          this.errorMessage = error.message || 'Login failed. Please try again.';
        }
      });
    }
  }

  loginAsDemo(role: UserRole) {
    this.authService.switchUser(role);
    this.router.navigate(['/dashboard']);
  }
}
