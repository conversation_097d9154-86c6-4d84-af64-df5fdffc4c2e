import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { of, throwError } from 'rxjs';

import { BugWatchersComponent } from './bug-watchers.component';
import { BugService } from '../../../core/services/bug.service';
import { UserService } from '../../../core/services/user.service';
import { AuthService } from '../../../core/services/auth.service';
import { BugWatcher, WatcherRequest, DEFAULT_WATCHER_PREFERENCES } from '../../../core/models/bug.model';
import { UserRole } from '../../../core/models/user.model';

describe('BugWatchersComponent', () => {
  let component: BugWatchersComponent;
  let fixture: ComponentFixture<BugWatchersComponent>;
  let bugService: jasmine.SpyObj<BugService>;
  let userService: jasmine.SpyObj<UserService>;
  let authService: jasmine.SpyObj<AuthService>;

  const mockUser = {
    id: 'user-1',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    fullName: 'Test User',
    role: UserRole.DEVELOPER,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  const mockAdminUser = {
    id: 'admin-1',
    email: '<EMAIL>',
    firstName: 'Admin',
    lastName: 'User',
    fullName: 'Admin User',
    role: UserRole.ADMIN,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  const mockWatcher: BugWatcher = {
    id: 'watcher-1',
    bugId: 'bug-1',
    userId: 'user-1',
    user: mockUser,
    watchedAt: new Date(),
    notificationPreferences: DEFAULT_WATCHER_PREFERENCES,
    addedById: 'admin-1',
    addedBy: mockAdminUser
  };

  beforeEach(async () => {
    const bugServiceSpy = jasmine.createSpyObj('BugService', [
      'addWatcher', 
      'removeWatcher', 
      'updateWatcherPreferences',
      'isUserWatching',
      'getBugWatchers'
    ]);
    const userServiceSpy = jasmine.createSpyObj('UserService', ['getAllUsers']);
    const authServiceSpy = jasmine.createSpyObj('AuthService', ['getCurrentUser']);

    await TestBed.configureTestingModule({
      imports: [
        BugWatchersComponent,
        ReactiveFormsModule
      ],
      providers: [
        { provide: BugService, useValue: bugServiceSpy },
        { provide: UserService, useValue: userServiceSpy },
        { provide: AuthService, useValue: authServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(BugWatchersComponent);
    component = fixture.componentInstance;
    
    bugService = TestBed.inject(BugService) as jasmine.SpyObj<BugService>;
    userService = TestBed.inject(UserService) as jasmine.SpyObj<UserService>;
    authService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;

    // Setup default spy returns
    userService.getAllUsers.and.returnValue([mockUser, mockAdminUser]);
    authService.getCurrentUser.and.returnValue(mockUser);

    // Set component inputs
    component.bugId = 'bug-1';
    component.watchers = [mockWatcher];
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize forms on init', () => {
    fixture.detectChanges();
    
    expect(component.addWatcherForm).toBeDefined();
    expect(component.preferencesForm).toBeDefined();
    expect(component.addWatcherForm.get('userId')?.value).toBe('');
    expect(component.addWatcherForm.get('emailNotifications')?.value).toBe(true);
  });

  it('should load users and update watch status on init', () => {
    fixture.detectChanges();
    
    expect(userService.getAllUsers).toHaveBeenCalled();
    expect(component.allUsers.length).toBe(2);
    expect(component.isCurrentUserWatching).toBeTruthy();
    expect(component.currentUserWatcher).toEqual(mockWatcher);
  });

  it('should update available users excluding current watchers', () => {
    fixture.detectChanges();
    
    expect(component.availableUsers.length).toBe(1); // Should exclude the current watcher
    expect(component.availableUsers[0].id).toBe('admin-1');
  });

  it('should check if user can manage watchers', () => {
    fixture.detectChanges();
    
    // Developer should not be able to manage watchers
    expect(component.canManageWatchers()).toBeFalsy();
    
    // Admin should be able to manage watchers
    authService.getCurrentUser.and.returnValue(mockAdminUser);
    component.currentUser = mockAdminUser;
    expect(component.canManageWatchers()).toBeTruthy();
    
    // Project Manager should be able to manage watchers
    const pmUser = { ...mockUser, role: UserRole.PROJECT_MANAGER };
    authService.getCurrentUser.and.returnValue(pmUser);
    component.currentUser = pmUser;
    expect(component.canManageWatchers()).toBeTruthy();
  });

  it('should check if user can remove watcher', () => {
    fixture.detectChanges();
    
    // User can remove themselves
    expect(component.canRemoveWatcher(mockWatcher)).toBeTruthy();
    
    // User cannot remove others (unless admin)
    const otherWatcher = { ...mockWatcher, userId: 'other-user' };
    expect(component.canRemoveWatcher(otherWatcher)).toBeFalsy();
    
    // Admin can remove anyone
    authService.getCurrentUser.and.returnValue(mockAdminUser);
    component.currentUser = mockAdminUser;
    expect(component.canRemoveWatcher(otherWatcher)).toBeTruthy();
  });

  it('should show watcher preferences for current user or admin', () => {
    fixture.detectChanges();
    
    // User can see their own preferences
    expect(component.showWatcherPreferences(mockWatcher)).toBeTruthy();
    
    // User cannot see others' preferences
    const otherWatcher = { ...mockWatcher, userId: 'other-user' };
    expect(component.showWatcherPreferences(otherWatcher)).toBeFalsy();
    
    // Admin can see anyone's preferences
    authService.getCurrentUser.and.returnValue(mockAdminUser);
    component.currentUser = mockAdminUser;
    expect(component.showWatcherPreferences(otherWatcher)).toBeTruthy();
  });

  it('should toggle current user watch status - start watching', () => {
    component.isCurrentUserWatching = false;
    component.currentUserWatcher = null;
    
    bugService.addWatcher.and.returnValue(of({
      success: true,
      data: mockWatcher,
      message: 'Started watching',
      timestamp: new Date()
    }));
    
    fixture.detectChanges();
    
    component.toggleCurrentUserWatch();
    
    expect(bugService.addWatcher).toHaveBeenCalledWith('bug-1', jasmine.objectContaining({
      userId: 'user-1'
    }));
  });

  it('should toggle current user watch status - stop watching', () => {
    component.isCurrentUserWatching = true;
    component.currentUserWatcher = mockWatcher;
    
    bugService.removeWatcher.and.returnValue(of({
      success: true,
      data: undefined,
      message: 'Stopped watching',
      timestamp: new Date()
    }));
    
    fixture.detectChanges();
    
    component.toggleCurrentUserWatch();
    
    expect(bugService.removeWatcher).toHaveBeenCalledWith('bug-1', 'user-1');
  });

  it('should handle watch toggle error', () => {
    component.isCurrentUserWatching = false;
    
    bugService.addWatcher.and.returnValue(throwError(() => new Error('Failed to start watching')));
    
    fixture.detectChanges();
    
    component.toggleCurrentUserWatch();
    
    expect(component.errorMessage).toContain('Failed to start watching');
    expect(component.updating).toBeFalsy();
  });

  it('should add watcher successfully', () => {
    bugService.addWatcher.and.returnValue(of({
      success: true,
      data: mockWatcher,
      message: 'Watcher added',
      timestamp: new Date()
    }));
    
    fixture.detectChanges();
    
    component.addWatcherForm.patchValue({
      userId: 'admin-1',
      emailNotifications: true,
      onComment: true
    });
    
    component.addWatcher();
    
    expect(bugService.addWatcher).toHaveBeenCalledWith('bug-1', jasmine.objectContaining({
      userId: 'admin-1',
      notificationPreferences: jasmine.objectContaining({
        emailNotifications: true,
        onComment: true
      })
    }));
    expect(component.showAddWatcherModal).toBeFalsy();
  });

  it('should handle add watcher error', () => {
    bugService.addWatcher.and.returnValue(throwError(() => new Error('Failed to add watcher')));
    
    fixture.detectChanges();
    
    component.addWatcherForm.patchValue({
      userId: 'admin-1'
    });
    
    component.addWatcher();
    
    expect(component.errorMessage).toContain('Failed to add watcher');
    expect(component.submitting).toBeFalsy();
  });

  it('should not add watcher with invalid form', () => {
    fixture.detectChanges();
    
    // Leave userId empty (invalid)
    component.addWatcher();
    
    expect(bugService.addWatcher).not.toHaveBeenCalled();
  });

  it('should update watcher preferences', () => {
    bugService.updateWatcherPreferences.and.returnValue(of({
      success: true,
      data: { ...mockWatcher, notificationPreferences: { ...DEFAULT_WATCHER_PREFERENCES, emailNotifications: false } },
      message: 'Preferences updated',
      timestamp: new Date()
    }));
    
    fixture.detectChanges();
    
    component.preferencesForm.patchValue({
      emailNotifications: false,
      onComment: false
    });
    
    component.updatePreferences();
    
    expect(bugService.updateWatcherPreferences).toHaveBeenCalledWith('bug-1', 'user-1', jasmine.objectContaining({
      emailNotifications: false,
      onComment: false
    }));
    expect(component.showPreferencesModal).toBeFalsy();
  });

  it('should remove watcher with confirmation', () => {
    bugService.removeWatcher.and.returnValue(of({
      success: true,
      data: undefined,
      message: 'Watcher removed',
      timestamp: new Date()
    }));
    
    spyOn(window, 'confirm').and.returnValue(true);
    fixture.detectChanges();
    
    component.removeWatcher(mockWatcher);
    
    expect(window.confirm).toHaveBeenCalled();
    expect(bugService.removeWatcher).toHaveBeenCalledWith('bug-1', 'user-1');
  });

  it('should not remove watcher if user cancels', () => {
    spyOn(window, 'confirm').and.returnValue(false);
    fixture.detectChanges();
    
    component.removeWatcher(mockWatcher);
    
    expect(bugService.removeWatcher).not.toHaveBeenCalled();
  });

  it('should close add watcher modal and reset form', () => {
    fixture.detectChanges();
    
    component.showAddWatcherModal = true;
    component.addWatcherForm.patchValue({ userId: 'admin-1' });
    
    component.closeAddWatcherModal();
    
    expect(component.showAddWatcherModal).toBeFalsy();
    expect(component.addWatcherForm.get('userId')?.value).toBe('');
  });

  it('should close preferences modal and reset form', () => {
    fixture.detectChanges();
    
    component.showPreferencesModal = true;
    component.preferencesForm.patchValue({ emailNotifications: false });
    
    component.closePreferencesModal();
    
    expect(component.showPreferencesModal).toBeFalsy();
    // Should restore original preferences if current user is watching
    if (component.currentUserWatcher) {
      expect(component.preferencesForm.get('emailNotifications')?.value).toBe(true);
    }
  });

  it('should format date correctly', () => {
    const today = new Date();
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
    const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    expect(component.formatDate(today)).toBe('Today');
    expect(component.formatDate(yesterday)).toBe('Yesterday');
    expect(component.formatDate(lastWeek)).toContain('days ago');
  });

  it('should get user initials', () => {
    expect(component.getUserInitials(mockUser)).toBe('TU');
  });

  it('should track watchers by id', () => {
    const trackResult = component.trackByWatcherId(0, mockWatcher);
    expect(trackResult).toBe('watcher-1');
  });

  it('should check field validity', () => {
    fixture.detectChanges();
    
    const userIdControl = component.addWatcherForm.get('userId');
    
    expect(component.isFieldInvalid('userId')).toBeFalsy();
    
    userIdControl?.markAsTouched();
    expect(component.isFieldInvalid('userId')).toBeTruthy();
    
    userIdControl?.setValue('admin-1');
    expect(component.isFieldInvalid('userId')).toBeFalsy();
  });

  it('should handle retry load', () => {
    fixture.detectChanges();
    
    component.errorMessage = 'Some error';
    component.retryLoad();
    
    expect(component.errorMessage).toBe('');
  });

  it('should display watchers correctly', () => {
    fixture.detectChanges();
    
    const compiled = fixture.nativeElement;
    
    expect(compiled.querySelector('.watcher-name')?.textContent).toContain('Test User');
    expect(compiled.querySelector('.watcher-role')?.textContent).toContain('DEVELOPER');
  });

  it('should show empty state when no watchers', () => {
    component.watchers = [];
    fixture.detectChanges();
    
    const compiled = fixture.nativeElement;
    const emptyElement = compiled.querySelector('.watchers-empty');
    
    expect(emptyElement).toBeTruthy();
    expect(emptyElement.textContent).toContain('No watchers yet');
  });

  it('should show loading state', () => {
    component.loading = true;
    fixture.detectChanges();
    
    const compiled = fixture.nativeElement;
    const loadingElement = compiled.querySelector('.watchers-loading');
    
    expect(loadingElement).toBeTruthy();
    expect(loadingElement.textContent).toContain('Loading watchers');
  });

  it('should show error state', () => {
    component.errorMessage = 'Failed to load watchers';
    fixture.detectChanges();
    
    const compiled = fixture.nativeElement;
    const errorElement = compiled.querySelector('.watchers-error');
    
    expect(errorElement).toBeTruthy();
    expect(errorElement.textContent).toContain('Failed to load watchers');
  });

  it('should show current user watch status', () => {
    fixture.detectChanges();
    
    const compiled = fixture.nativeElement;
    const statusCard = compiled.querySelector('.status-card');
    
    expect(statusCard).toBeTruthy();
    expect(statusCard.classList.contains('watching')).toBeTruthy();
    expect(compiled.querySelector('.status-label')?.textContent).toContain('You are watching this bug');
  });

  it('should show add watcher button for authorized users', () => {
    authService.getCurrentUser.and.returnValue(mockAdminUser);
    component.currentUser = mockAdminUser;
    fixture.detectChanges();
    
    const compiled = fixture.nativeElement;
    const addButton = compiled.querySelector('.btn-primary');
    
    expect(addButton).toBeTruthy();
    expect(addButton.textContent).toContain('Add Watcher');
  });

  it('should not show add watcher button for unauthorized users', () => {
    // Developer should not see add watcher button
    fixture.detectChanges();
    
    const compiled = fixture.nativeElement;
    const addButton = compiled.querySelector('.btn-primary');
    
    expect(addButton).toBeFalsy();
  });
});
