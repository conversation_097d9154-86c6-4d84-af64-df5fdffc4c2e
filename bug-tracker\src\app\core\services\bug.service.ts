import { Injectable, inject } from '@angular/core';
import { Observable, BehaviorSubject, of, throwError } from 'rxjs';
import { delay, map } from 'rxjs/operators';
import {
  Bug,
  BugSummary,
  CreateBugRequest,
  UpdateBugRequest,
  BugStatus,
  BugSeverity,
  BugPriority,
  BugComment,
  BugAttachment,
  BugAuditLog,
  BugAuditAction,
  BugWatcher,
  WatcherRequest,
  WatcherNotificationPreferences,
  DEFAULT_WATCHER_PREFERENCES,
  BugFilter,
  BugListResponse,
  BugMetrics,
  BugWatcher,
  WatcherRequest,
  WatcherNotificationPreferences,
  DEFAULT_WATCHER_PREFERENCES
} from '../models/bug.model';
import { ApiResponse, PaginatedResponse, generateId } from '../models/common.model';
import { UserRole } from '../models/user.model';
import { UserService } from './user.service';
import { ProjectService } from './project.service';

@Injectable({
  providedIn: 'root'
})
export class BugService {
  private userService = inject(UserService);
  private projectService = inject(ProjectService);
  private bugsSubject = new BehaviorSubject<Bug[]>([]);
  private storageKey = 'bugtracker_bugs';

  bugs$ = this.bugsSubject.asObservable();

  constructor() {
    this.loadBugsFromStorage();
  }

  // CRUD Operations
  getBugs(filter?: BugFilter, page = 1, pageSize = 20): Observable<BugListResponse> {
    return this.bugs$.pipe(
      map(bugs => {
        let filteredBugs = [...bugs];

        // Apply filters
        if (filter) {
          if (filter.projectId) {
            filteredBugs = filteredBugs.filter(bug => bug.projectId === filter.projectId);
          }
          if (filter.status && filter.status.length > 0) {
            filteredBugs = filteredBugs.filter(bug => filter.status!.includes(bug.status));
          }
          if (filter.severity && filter.severity.length > 0) {
            filteredBugs = filteredBugs.filter(bug => filter.severity!.includes(bug.severity));
          }
          if (filter.priority && filter.priority.length > 0) {
            filteredBugs = filteredBugs.filter(bug => filter.priority!.includes(bug.priority));
          }
          if (filter.assignedToId) {
            filteredBugs = filteredBugs.filter(bug => 
              bug.qaAssignedId === filter.assignedToId || bug.devAssignedId === filter.assignedToId
            );
          }
          if (filter.reportedById) {
            filteredBugs = filteredBugs.filter(bug => bug.reportedById === filter.reportedById);
          }
          if (filter.search) {
            const searchLower = filter.search.toLowerCase();
            filteredBugs = filteredBugs.filter(bug => 
              bug.title.toLowerCase().includes(searchLower) ||
              bug.description.toLowerCase().includes(searchLower) ||
              bug.project.name.toLowerCase().includes(searchLower)
            );
          }
          if (filter.dateFrom) {
            filteredBugs = filteredBugs.filter(bug => new Date(bug.issueDate) >= filter.dateFrom!);
          }
          if (filter.dateTo) {
            filteredBugs = filteredBugs.filter(bug => new Date(bug.issueDate) <= filter.dateTo!);
          }
          if (filter.isOverdue !== undefined) {
            filteredBugs = filteredBugs.filter(bug => {
              const isOverdue = bug.expectedFixDate && new Date() > new Date(bug.expectedFixDate) && bug.status !== BugStatus.CLOSED;
              return filter.isOverdue ? isOverdue : !isOverdue;
            });
          }
        }

        // Apply sorting
        filteredBugs.sort((a, b) => {
          // Default sort by issue date (newest first)
          return new Date(b.issueDate).getTime() - new Date(a.issueDate).getTime();
        });

        // Apply pagination
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const paginatedBugs = filteredBugs.slice(startIndex, endIndex);

        return {
          bugs: paginatedBugs,
          totalCount: filteredBugs.length,
          page,
          pageSize,
          totalPages: Math.ceil(filteredBugs.length / pageSize)
        };
      }),
      delay(300)
    );
  }

  getBugById(id: string): Observable<Bug | null> {
    return this.bugs$.pipe(
      map(bugs => bugs.find(b => b.id === id) || null),
      delay(200)
    );
  }

  createBug(request: CreateBugRequest): Observable<ApiResponse<Bug>> {
    const currentUser = this.userService.getCurrentUser();
    if (!currentUser) {
      return throwError(() => new Error('User not authenticated'));
    }

    return this.projectService.getProjectById(request.projectId).pipe(
      delay(500),
      map(project => {
        if (!project) {
          throw new Error('Project not found');
        }

        const module = project.modules.find(m => m.id === request.moduleId);
        if (!module) {
          throw new Error('Module not found');
        }

        const feature = module.features.find(f => f.id === request.featureId);
        if (!feature) {
          throw new Error('Feature not found');
        }

        const newBug: Bug = {
          id: generateId(),
          title: request.title,
          description: request.description,
          stepsToReproduce: request.stepsToReproduce,
          projectId: request.projectId,
          project: project,
          moduleId: request.moduleId,
          module: module,
          featureId: request.featureId,
          feature: feature,
          severity: request.severity,
          priority: request.priority,
          status: BugStatus.NEW,
          qaAssignedId: request.qaAssignedId,
          qaAssigned: request.qaAssignedId ? this.userService.getUserById(request.qaAssignedId) || undefined : undefined,
          devAssignedId: request.devAssignedId,
          devAssigned: request.devAssignedId ? this.userService.getUserById(request.devAssignedId) || undefined : undefined,
          reportedById: currentUser.id,
          reportedBy: {
            id: currentUser.id,
            email: currentUser.email,
            firstName: currentUser.fullName.split(' ')[0] || '',
            lastName: currentUser.fullName.split(' ').slice(1).join(' ') || '',
            fullName: currentUser.fullName,
            role: currentUser.role,
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date()
          },
          issueDate: new Date(),
          expectedReviewDate: request.expectedReviewDate,
          expectedFixDate: request.expectedFixDate,
          attachments: [],
          comments: [],
          auditLogs: [],
          watchers: [],
          tags: request.tags || [],
          estimatedHours: request.estimatedHours,
          reopenCount: 0,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        // Add initial audit log
        const auditLog: BugAuditLog = {
          id: generateId(),
          bugId: newBug.id,
          action: BugAuditAction.CREATED,
          performedById: currentUser.id,
          performedBy: newBug.reportedBy,
          performedAt: new Date(),
          description: `Bug created by ${currentUser.fullName}`
        };
        newBug.auditLogs.push(auditLog);

        // Add initial watchers
        if (request.initialWatchers && request.initialWatchers.length > 0) {
          for (const watcherRequest of request.initialWatchers) {
            const watcherUser = this.userService.getUserById(watcherRequest.userId);
            if (watcherUser) {
              const watcher: BugWatcher = {
                id: generateId(),
                bugId: newBug.id,
                userId: watcherRequest.userId,
                user: watcherUser,
                watchedAt: new Date(),
                notificationPreferences: {
                  ...DEFAULT_WATCHER_PREFERENCES,
                  ...watcherRequest.notificationPreferences
                },
                addedById: currentUser.id,
                addedBy: newBug.reportedBy
              };
              newBug.watchers.push(watcher);

              // Add audit log for watcher
              const watcherAuditLog: BugAuditLog = {
                id: generateId(),
                bugId: newBug.id,
                action: BugAuditAction.WATCHER_ADDED,
                performedById: currentUser.id,
                performedBy: newBug.reportedBy,
                performedAt: new Date(),
                description: `${watcherUser.fullName} added as watcher`
              };
              newBug.auditLogs.push(watcherAuditLog);
            }
          }
        }

        // Automatically add reporter as watcher
        const reporterWatcher: BugWatcher = {
          id: generateId(),
          bugId: newBug.id,
          userId: currentUser.id,
          user: newBug.reportedBy,
          watchedAt: new Date(),
          notificationPreferences: DEFAULT_WATCHER_PREFERENCES,
          addedById: currentUser.id,
          addedBy: newBug.reportedBy
        };
        newBug.watchers.push(reporterWatcher);

        const currentBugs = this.bugsSubject.value;
        const updatedBugs = [...currentBugs, newBug];
        this.bugsSubject.next(updatedBugs);
        this.saveBugsToStorage(updatedBugs);

        return {
          success: true,
          data: newBug,
          message: 'Bug created successfully',
          timestamp: new Date()
        };
      })
    );
  }

  updateBug(id: string, request: UpdateBugRequest): Observable<ApiResponse<Bug>> {
    const currentUser = this.userService.getCurrentUser();
    if (!currentUser) {
      return throwError(() => new Error('User not authenticated'));
    }

    return this.bugs$.pipe(
      delay(300),
      map(bugs => {
        const bugIndex = bugs.findIndex(b => b.id === id);
        if (bugIndex === -1) {
          throw new Error('Bug not found');
        }

        const originalBug = bugs[bugIndex];
        const updatedBug = {
          ...originalBug,
          ...request,
          updatedAt: new Date()
        };

        // Update assigned users if changed
        if (request.qaAssignedId !== undefined) {
          updatedBug.qaAssigned = request.qaAssignedId ? this.userService.getUserById(request.qaAssignedId) || undefined : undefined;
        }
        if (request.devAssignedId !== undefined) {
          updatedBug.devAssigned = request.devAssignedId ? this.userService.getUserById(request.devAssignedId) || undefined : undefined;
        }

        // Create audit logs for changes
        const auditLogs: BugAuditLog[] = [];

        if (request.status && request.status !== originalBug.status) {
          auditLogs.push({
            id: generateId(),
            bugId: id,
            action: BugAuditAction.STATUS_CHANGED,
            field: 'status',
            oldValue: originalBug.status,
            newValue: request.status,
            performedById: currentUser.id,
            performedBy: {
              id: currentUser.id,
              email: currentUser.email,
              firstName: currentUser.fullName.split(' ')[0] || '',
              lastName: currentUser.fullName.split(' ').slice(1).join(' ') || '',
              fullName: currentUser.fullName,
              role: currentUser.role,
              isActive: true,
              createdAt: new Date(),
              updatedAt: new Date()
            },
            performedAt: new Date(),
            description: `Status changed from ${originalBug.status} to ${request.status}`
          });

          // Handle reopen count
          if (request.status === BugStatus.REOPENED) {
            updatedBug.reopenCount = originalBug.reopenCount + 1;
          }

          // Set dates based on status
          if (request.status === BugStatus.FIXED && !updatedBug.actualFixDate) {
            updatedBug.actualFixDate = new Date();
          }
          if (request.status === BugStatus.CLOSED && !updatedBug.closedDate) {
            updatedBug.closedDate = new Date();
          }
          if (request.status === BugStatus.READY_FOR_RETEST && !updatedBug.retestDate) {
            updatedBug.retestDate = new Date();
          }
        }

        if (request.qaAssignedId !== originalBug.qaAssignedId) {
          auditLogs.push({
            id: generateId(),
            bugId: id,
            action: request.qaAssignedId ? BugAuditAction.ASSIGNED : BugAuditAction.UNASSIGNED,
            field: 'qaAssignedId',
            oldValue: originalBug.qaAssigned?.fullName || 'Unassigned',
            newValue: updatedBug.qaAssigned?.fullName || 'Unassigned',
            performedById: currentUser.id,
            performedBy: {
              id: currentUser.id,
              email: currentUser.email,
              firstName: currentUser.fullName.split(' ')[0] || '',
              lastName: currentUser.fullName.split(' ').slice(1).join(' ') || '',
              fullName: currentUser.fullName,
              role: currentUser.role,
              isActive: true,
              createdAt: new Date(),
              updatedAt: new Date()
            },
            performedAt: new Date(),
            description: `QA assignment changed from ${originalBug.qaAssigned?.fullName || 'Unassigned'} to ${updatedBug.qaAssigned?.fullName || 'Unassigned'}`
          });
        }

        if (request.devAssignedId !== originalBug.devAssignedId) {
          auditLogs.push({
            id: generateId(),
            bugId: id,
            action: request.devAssignedId ? BugAuditAction.ASSIGNED : BugAuditAction.UNASSIGNED,
            field: 'devAssignedId',
            oldValue: originalBug.devAssigned?.fullName || 'Unassigned',
            newValue: updatedBug.devAssigned?.fullName || 'Unassigned',
            performedById: currentUser.id,
            performedBy: {
              id: currentUser.id,
              email: currentUser.email,
              firstName: currentUser.fullName.split(' ')[0] || '',
              lastName: currentUser.fullName.split(' ').slice(1).join(' ') || '',
              fullName: currentUser.fullName,
              role: currentUser.role,
              isActive: true,
              createdAt: new Date(),
              updatedAt: new Date()
            },
            performedAt: new Date(),
            description: `Developer assignment changed from ${originalBug.devAssigned?.fullName || 'Unassigned'} to ${updatedBug.devAssigned?.fullName || 'Unassigned'}`
          });
        }

        // Add audit logs to bug
        updatedBug.auditLogs = [...originalBug.auditLogs, ...auditLogs];

        const updatedBugs = [...bugs];
        updatedBugs[bugIndex] = updatedBug;

        this.bugsSubject.next(updatedBugs);
        this.saveBugsToStorage(updatedBugs);

        return {
          success: true,
          data: updatedBug,
          message: 'Bug updated successfully',
          timestamp: new Date()
        };
      })
    );
  }

  deleteBug(id: string): Observable<ApiResponse<void>> {
    return this.bugs$.pipe(
      delay(300),
      map(bugs => {
        const bugExists = bugs.some(b => b.id === id);
        if (!bugExists) {
          throw new Error('Bug not found');
        }

        const updatedBugs = bugs.filter(b => b.id !== id);
        this.bugsSubject.next(updatedBugs);
        this.saveBugsToStorage(updatedBugs);

        return {
          success: true,
          message: 'Bug deleted successfully',
          timestamp: new Date()
        };
      })
    );
  }

  // Comment operations
  addComment(bugId: string, content: string, isInternal = false): Observable<ApiResponse<BugComment>> {
    const currentUser = this.userService.getCurrentUser();
    if (!currentUser) {
      return throwError(() => new Error('User not authenticated'));
    }

    return this.bugs$.pipe(
      delay(300),
      map(bugs => {
        const bugIndex = bugs.findIndex(b => b.id === bugId);
        if (bugIndex === -1) {
          throw new Error('Bug not found');
        }

        const newComment: BugComment = {
          id: generateId(),
          bugId: bugId,
          content: content,
          authorId: currentUser.id,
          author: {
            id: currentUser.id,
            email: currentUser.email,
            firstName: currentUser.fullName.split(' ')[0] || '',
            lastName: currentUser.fullName.split(' ').slice(1).join(' ') || '',
            fullName: currentUser.fullName,
            role: currentUser.role,
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date()
          },
          createdAt: new Date(),
          updatedAt: new Date(),
          isInternal: isInternal,
          mentionedUsers: [],
          attachments: []
        };

        const updatedBug = { ...bugs[bugIndex] };
        updatedBug.comments = [...updatedBug.comments, newComment];

        // Add audit log
        const auditLog: BugAuditLog = {
          id: generateId(),
          bugId: bugId,
          action: BugAuditAction.COMMENTED,
          performedById: currentUser.id,
          performedBy: newComment.author,
          performedAt: new Date(),
          description: `Comment added by ${currentUser.fullName}`
        };
        updatedBug.auditLogs = [...updatedBug.auditLogs, auditLog];
        updatedBug.updatedAt = new Date();

        const updatedBugs = [...bugs];
        updatedBugs[bugIndex] = updatedBug;

        this.bugsSubject.next(updatedBugs);
        this.saveBugsToStorage(updatedBugs);

        return {
          success: true,
          data: newComment,
          message: 'Comment added successfully',
          timestamp: new Date()
        };
      })
    );
  }

  updateComment(bugId: string, commentId: string, content: string): Observable<ApiResponse<BugComment>> {
    const currentUser = this.userService.getCurrentUser();
    if (!currentUser) {
      return throwError(() => new Error('User not authenticated'));
    }

    return this.bugs$.pipe(
      delay(300),
      map(bugs => {
        const bugIndex = bugs.findIndex(b => b.id === bugId);
        if (bugIndex === -1) {
          throw new Error('Bug not found');
        }

        const updatedBug = { ...bugs[bugIndex] };
        const commentIndex = updatedBug.comments.findIndex(c => c.id === commentId);
        if (commentIndex === -1) {
          throw new Error('Comment not found');
        }

        const comment = updatedBug.comments[commentIndex];
        if (comment.authorId !== currentUser.id) {
          throw new Error('You can only edit your own comments');
        }

        const updatedComment = {
          ...comment,
          content: content,
          updatedAt: new Date()
        };

        updatedBug.comments = [...updatedBug.comments];
        updatedBug.comments[commentIndex] = updatedComment;
        updatedBug.updatedAt = new Date();

        const updatedBugs = [...bugs];
        updatedBugs[bugIndex] = updatedBug;

        this.bugsSubject.next(updatedBugs);
        this.saveBugsToStorage(updatedBugs);

        return {
          success: true,
          data: updatedComment,
          message: 'Comment updated successfully',
          timestamp: new Date()
        };
      })
    );
  }

  deleteComment(bugId: string, commentId: string): Observable<ApiResponse<void>> {
    const currentUser = this.userService.getCurrentUser();
    if (!currentUser) {
      return throwError(() => new Error('User not authenticated'));
    }

    return this.bugs$.pipe(
      delay(300),
      map(bugs => {
        const bugIndex = bugs.findIndex(b => b.id === bugId);
        if (bugIndex === -1) {
          throw new Error('Bug not found');
        }

        const updatedBug = { ...bugs[bugIndex] };
        const commentIndex = updatedBug.comments.findIndex(c => c.id === commentId);
        if (commentIndex === -1) {
          throw new Error('Comment not found');
        }

        const comment = updatedBug.comments[commentIndex];
        if (comment.authorId !== currentUser.id) {
          throw new Error('You can only delete your own comments');
        }

        updatedBug.comments = updatedBug.comments.filter(c => c.id !== commentId);
        updatedBug.updatedAt = new Date();

        const updatedBugs = [...bugs];
        updatedBugs[bugIndex] = updatedBug;

        this.bugsSubject.next(updatedBugs);
        this.saveBugsToStorage(updatedBugs);

        return {
          success: true,
          data: undefined,
          message: 'Comment deleted successfully',
          timestamp: new Date()
        };
      })
    );
  }

  // Watcher operations
  addWatcher(bugId: string, watcherRequest: WatcherRequest): Observable<ApiResponse<BugWatcher>> {
    const currentUser = this.userService.getCurrentUser();
    if (!currentUser) {
      return throwError(() => new Error('User not authenticated'));
    }

    return this.bugs$.pipe(
      delay(300),
      map(bugs => {
        const bugIndex = bugs.findIndex(b => b.id === bugId);
        if (bugIndex === -1) {
          throw new Error('Bug not found');
        }

        const targetUser = this.userService.getUserById(watcherRequest.userId);
        if (!targetUser) {
          throw new Error('User not found');
        }

        const updatedBug = { ...bugs[bugIndex] };

        // Check if user is already watching
        const existingWatcher = updatedBug.watchers.find(w => w.userId === watcherRequest.userId);
        if (existingWatcher) {
          throw new Error('User is already watching this bug');
        }

        const newWatcher: BugWatcher = {
          id: generateId(),
          bugId: bugId,
          userId: watcherRequest.userId,
          user: targetUser,
          watchedAt: new Date(),
          notificationPreferences: {
            ...DEFAULT_WATCHER_PREFERENCES,
            ...watcherRequest.notificationPreferences
          },
          addedById: currentUser.id,
          addedBy: {
            id: currentUser.id,
            email: currentUser.email,
            firstName: currentUser.fullName.split(' ')[0] || '',
            lastName: currentUser.fullName.split(' ').slice(1).join(' ') || '',
            fullName: currentUser.fullName,
            role: currentUser.role,
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        };

        updatedBug.watchers = [...updatedBug.watchers, newWatcher];

        // Add audit log
        const auditLog: BugAuditLog = {
          id: generateId(),
          bugId: bugId,
          action: BugAuditAction.WATCHER_ADDED,
          performedById: currentUser.id,
          performedBy: newWatcher.addedBy,
          performedAt: new Date(),
          description: `${targetUser.fullName} added as watcher by ${currentUser.fullName}`
        };
        updatedBug.auditLogs = [...updatedBug.auditLogs, auditLog];
        updatedBug.updatedAt = new Date();

        const updatedBugs = [...bugs];
        updatedBugs[bugIndex] = updatedBug;

        this.bugsSubject.next(updatedBugs);
        this.saveBugsToStorage(updatedBugs);

        return {
          success: true,
          data: newWatcher,
          message: 'Watcher added successfully',
          timestamp: new Date()
        };
      })
    );
  }

  removeWatcher(bugId: string, userId: string): Observable<ApiResponse<void>> {
    const currentUser = this.userService.getCurrentUser();
    if (!currentUser) {
      return throwError(() => new Error('User not authenticated'));
    }

    return this.bugs$.pipe(
      delay(300),
      map(bugs => {
        const bugIndex = bugs.findIndex(b => b.id === bugId);
        if (bugIndex === -1) {
          throw new Error('Bug not found');
        }

        const updatedBug = { ...bugs[bugIndex] };
        const watcherIndex = updatedBug.watchers.findIndex(w => w.userId === userId);

        if (watcherIndex === -1) {
          throw new Error('User is not watching this bug');
        }

        const removedWatcher = updatedBug.watchers[watcherIndex];

        // Check permissions - users can remove themselves, or admins can remove anyone
        if (userId !== currentUser.id && currentUser.role !== UserRole.ADMIN) {
          throw new Error('You can only remove yourself as a watcher');
        }

        updatedBug.watchers = updatedBug.watchers.filter(w => w.userId !== userId);

        // Add audit log
        const auditLog: BugAuditLog = {
          id: generateId(),
          bugId: bugId,
          action: BugAuditAction.WATCHER_REMOVED,
          performedById: currentUser.id,
          performedBy: {
            id: currentUser.id,
            email: currentUser.email,
            firstName: currentUser.fullName.split(' ')[0] || '',
            lastName: currentUser.fullName.split(' ').slice(1).join(' ') || '',
            fullName: currentUser.fullName,
            role: currentUser.role,
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date()
          },
          performedAt: new Date(),
          description: `${removedWatcher.user.fullName} removed as watcher by ${currentUser.fullName}`
        };
        updatedBug.auditLogs = [...updatedBug.auditLogs, auditLog];
        updatedBug.updatedAt = new Date();

        const updatedBugs = [...bugs];
        updatedBugs[bugIndex] = updatedBug;

        this.bugsSubject.next(updatedBugs);
        this.saveBugsToStorage(updatedBugs);

        return {
          success: true,
          data: undefined,
          message: 'Watcher removed successfully',
          timestamp: new Date()
        };
      })
    );
  }

  updateWatcherPreferences(bugId: string, userId: string, preferences: Partial<WatcherNotificationPreferences>): Observable<ApiResponse<BugWatcher>> {
    const currentUser = this.userService.getCurrentUser();
    if (!currentUser) {
      return throwError(() => new Error('User not authenticated'));
    }

    return this.bugs$.pipe(
      delay(300),
      map(bugs => {
        const bugIndex = bugs.findIndex(b => b.id === bugId);
        if (bugIndex === -1) {
          throw new Error('Bug not found');
        }

        const updatedBug = { ...bugs[bugIndex] };
        const watcherIndex = updatedBug.watchers.findIndex(w => w.userId === userId);

        if (watcherIndex === -1) {
          throw new Error('User is not watching this bug');
        }

        // Check permissions - users can only update their own preferences
        if (userId !== currentUser.id) {
          throw new Error('You can only update your own notification preferences');
        }

        const updatedWatcher = {
          ...updatedBug.watchers[watcherIndex],
          notificationPreferences: {
            ...updatedBug.watchers[watcherIndex].notificationPreferences,
            ...preferences
          }
        };

        updatedBug.watchers = [...updatedBug.watchers];
        updatedBug.watchers[watcherIndex] = updatedWatcher;
        updatedBug.updatedAt = new Date();

        const updatedBugs = [...bugs];
        updatedBugs[bugIndex] = updatedBug;

        this.bugsSubject.next(updatedBugs);
        this.saveBugsToStorage(updatedBugs);

        return {
          success: true,
          data: updatedWatcher,
          message: 'Watcher preferences updated successfully',
          timestamp: new Date()
        };
      })
    );
  }

  isUserWatching(bugId: string, userId: string): Observable<boolean> {
    return this.bugs$.pipe(
      map(bugs => {
        const bug = bugs.find(b => b.id === bugId);
        if (!bug) return false;
        return bug.watchers.some(w => w.userId === userId);
      })
    );
  }

  getBugWatchers(bugId: string): Observable<BugWatcher[]> {
    return this.bugs$.pipe(
      map(bugs => {
        const bug = bugs.find(b => b.id === bugId);
        return bug ? bug.watchers : [];
      })
    );
  }

  // Utility methods
  getBugSummaries(filter?: BugFilter): Observable<BugSummary[]> {
    return this.getBugs(filter, 1, 1000).pipe(
      map(response => response.bugs.map(bug => ({
        id: bug.id,
        title: bug.title,
        projectName: bug.project.name,
        moduleName: bug.module.name,
        featureName: bug.feature.name,
        severity: bug.severity,
        priority: bug.priority,
        status: bug.status,
        qaAssigned: bug.qaAssigned?.fullName,
        devAssigned: bug.devAssigned?.fullName,
        reportedBy: bug.reportedBy.fullName,
        issueDate: bug.issueDate,
        expectedFixDate: bug.expectedFixDate,
        actualFixDate: bug.actualFixDate,
        isOverdue: bug.expectedFixDate ? new Date() > new Date(bug.expectedFixDate) && bug.status !== BugStatus.CLOSED : false,
        daysSinceReported: Math.ceil((new Date().getTime() - new Date(bug.issueDate).getTime()) / (1000 * 60 * 60 * 24))
      })))
    );
  }

  getBugMetrics(projectId?: string): Observable<BugMetrics> {
    return this.bugs$.pipe(
      map(bugs => {
        let filteredBugs = bugs;
        if (projectId) {
          filteredBugs = bugs.filter(bug => bug.projectId === projectId);
        }

        const totalBugs = filteredBugs.length;
        const openBugs = filteredBugs.filter(bug => bug.status !== BugStatus.CLOSED).length;
        const closedBugs = filteredBugs.filter(bug => bug.status === BugStatus.CLOSED).length;
        const criticalBugs = filteredBugs.filter(bug => bug.severity === BugSeverity.CRITICAL).length;
        const overdueBugs = filteredBugs.filter(bug =>
          bug.expectedFixDate && new Date() > new Date(bug.expectedFixDate) && bug.status !== BugStatus.CLOSED
        ).length;

        const bugsByStatus = {
          [BugStatus.NEW]: filteredBugs.filter(bug => bug.status === BugStatus.NEW).length,
          [BugStatus.IN_PROGRESS]: filteredBugs.filter(bug => bug.status === BugStatus.IN_PROGRESS).length,
          [BugStatus.FIXED]: filteredBugs.filter(bug => bug.status === BugStatus.FIXED).length,
          [BugStatus.READY_FOR_RETEST]: filteredBugs.filter(bug => bug.status === BugStatus.READY_FOR_RETEST).length,
          [BugStatus.CLOSED]: filteredBugs.filter(bug => bug.status === BugStatus.CLOSED).length,
          [BugStatus.REOPENED]: filteredBugs.filter(bug => bug.status === BugStatus.REOPENED).length,
          [BugStatus.REJECTED]: filteredBugs.filter(bug => bug.status === BugStatus.REJECTED).length,
          [BugStatus.DUPLICATE]: filteredBugs.filter(bug => bug.status === BugStatus.DUPLICATE).length
        };

        const bugsBySeverity = {
          [BugSeverity.LOW]: filteredBugs.filter(bug => bug.severity === BugSeverity.LOW).length,
          [BugSeverity.MEDIUM]: filteredBugs.filter(bug => bug.severity === BugSeverity.MEDIUM).length,
          [BugSeverity.HIGH]: filteredBugs.filter(bug => bug.severity === BugSeverity.HIGH).length,
          [BugSeverity.CRITICAL]: filteredBugs.filter(bug => bug.severity === BugSeverity.CRITICAL).length
        };

        const bugsByPriority = {
          [BugPriority.LOW]: filteredBugs.filter(bug => bug.priority === BugPriority.LOW).length,
          [BugPriority.MEDIUM]: filteredBugs.filter(bug => bug.priority === BugPriority.MEDIUM).length,
          [BugPriority.HIGH]: filteredBugs.filter(bug => bug.priority === BugPriority.HIGH).length,
          [BugPriority.CRITICAL]: filteredBugs.filter(bug => bug.priority === BugPriority.CRITICAL).length
        };

        return {
          totalBugs,
          openBugs,
          closedBugs,
          criticalBugs,
          overdueBugs,
          bugsByStatus,
          bugsBySeverity,
          bugsByPriority,
          averageTimeToFix: this.calculateAverageTimeToFix(filteredBugs),
          reopenRate: this.calculateReopenRate(filteredBugs)
        };
      }),
      delay(200)
    );
  }

  // Helper methods
  private calculateAverageTimeToFix(bugs: Bug[]): number {
    const fixedBugs = bugs.filter(bug => bug.actualFixDate && bug.issueDate);
    if (fixedBugs.length === 0) return 0;

    const totalTime = fixedBugs.reduce((sum, bug) => {
      const issueTime = new Date(bug.issueDate).getTime();
      const fixTime = new Date(bug.actualFixDate!).getTime();
      return sum + (fixTime - issueTime);
    }, 0);

    return Math.round(totalTime / fixedBugs.length / (1000 * 60 * 60 * 24)); // Convert to days
  }

  private calculateReopenRate(bugs: Bug[]): number {
    const closedBugs = bugs.filter(bug => bug.status === BugStatus.CLOSED || bug.reopenCount > 0);
    if (closedBugs.length === 0) return 0;

    const reopenedBugs = bugs.filter(bug => bug.reopenCount > 0);
    return Math.round((reopenedBugs.length / closedBugs.length) * 100);
  }

  // Storage methods
  private loadBugsFromStorage(): void {
    if (typeof localStorage !== 'undefined') {
      const stored = localStorage.getItem(this.storageKey);
      if (stored) {
        try {
          const bugs = JSON.parse(stored);
          // Convert date strings back to Date objects
          const processedBugs = bugs.map((bug: any) => ({
            ...bug,
            issueDate: new Date(bug.issueDate),
            expectedReviewDate: bug.expectedReviewDate ? new Date(bug.expectedReviewDate) : undefined,
            expectedFixDate: bug.expectedFixDate ? new Date(bug.expectedFixDate) : undefined,
            actualFixDate: bug.actualFixDate ? new Date(bug.actualFixDate) : undefined,
            retestDate: bug.retestDate ? new Date(bug.retestDate) : undefined,
            closedDate: bug.closedDate ? new Date(bug.closedDate) : undefined,
            createdAt: new Date(bug.createdAt),
            updatedAt: new Date(bug.updatedAt),
            comments: bug.comments.map((comment: any) => ({
              ...comment,
              createdAt: new Date(comment.createdAt),
              updatedAt: new Date(comment.updatedAt)
            })),
            auditLogs: bug.auditLogs.map((log: any) => ({
              ...log,
              performedAt: new Date(log.performedAt)
            })),
            attachments: bug.attachments.map((attachment: any) => ({
              ...attachment,
              uploadedAt: new Date(attachment.uploadedAt)
            }))
          }));
          this.bugsSubject.next(processedBugs);
        } catch (error) {
          console.error('Error loading bugs from storage:', error);
          this.initializeMockData();
        }
      } else {
        this.initializeMockData();
      }
    } else {
      this.initializeMockData();
    }
  }

  private saveBugsToStorage(bugs: Bug[]): void {
    if (typeof localStorage !== 'undefined') {
      try {
        localStorage.setItem(this.storageKey, JSON.stringify(bugs));
      } catch (error) {
        console.error('Error saving bugs to storage:', error);
      }
    }
  }

  private initializeMockData(): void {
    // Initialize with some mock bugs for development
    const mockBugs = this.generateMockBugs();
    this.bugsSubject.next(mockBugs);
    this.saveBugsToStorage(mockBugs);
  }

  private generateMockBugs(): Bug[] {
    const projects = this.projectService.getAllProjects();
    const users = this.userService.getAllUsersSync();

    if (projects.length === 0 || users.length === 0) {
      return [];
    }

    const mockBugs: Bug[] = [];
    const bugTitles = [
      'Login form validation error',
      'Dashboard loading issue',
      'Payment gateway timeout',
      'User profile image upload fails',
      'Search functionality not working',
      'Email notifications not sent',
      'Mobile responsive layout broken',
      'Database connection timeout',
      'File download corrupted',
      'Session timeout too short'
    ];

    const descriptions = [
      'Users are unable to login due to validation errors in the form',
      'Dashboard takes too long to load and sometimes fails completely',
      'Payment processing fails with timeout errors during checkout',
      'Profile image upload feature is not working for large files',
      'Search results are not displaying correctly for certain queries',
      'Email notifications are not being sent to users after actions',
      'Mobile layout is broken on devices with smaller screen sizes',
      'Application frequently loses database connection causing errors',
      'Downloaded files are corrupted and cannot be opened',
      'User sessions expire too quickly causing frequent re-logins'
    ];

    for (let i = 0; i < Math.min(10, projects.length * 2); i++) {
      const project = projects[i % projects.length];
      const module = project.modules[0]; // Use first module
      const feature = module?.features[0]; // Use first feature

      if (!module || !feature) continue;

      const reportedBy = users[i % users.length];
      const qaAssigned = users[(i + 1) % users.length];
      const devAssigned = users[(i + 2) % users.length];

      const issueDate = new Date();
      issueDate.setDate(issueDate.getDate() - Math.floor(Math.random() * 30));

      const expectedFixDate = new Date(issueDate);
      expectedFixDate.setDate(expectedFixDate.getDate() + Math.floor(Math.random() * 14) + 1);

      const statuses = Object.values(BugStatus);
      const severities = Object.values(BugSeverity);
      const priorities = Object.values(BugPriority);

      const status = statuses[Math.floor(Math.random() * statuses.length)];
      let actualFixDate: Date | undefined;
      let closedDate: Date | undefined;

      if (status === BugStatus.FIXED || status === BugStatus.CLOSED) {
        actualFixDate = new Date(issueDate);
        actualFixDate.setDate(actualFixDate.getDate() + Math.floor(Math.random() * 10) + 1);
      }

      if (status === BugStatus.CLOSED) {
        closedDate = new Date(actualFixDate || issueDate);
        closedDate.setDate(closedDate.getDate() + Math.floor(Math.random() * 3) + 1);
      }

      const bug: Bug = {
        id: generateId(),
        title: bugTitles[i % bugTitles.length],
        description: descriptions[i % descriptions.length],
        stepsToReproduce: `1. Navigate to ${feature.name}\n2. Perform the action\n3. Observe the error`,
        projectId: project.id,
        project: project,
        moduleId: module.id,
        module: module,
        featureId: feature.id,
        feature: feature,
        severity: severities[Math.floor(Math.random() * severities.length)],
        priority: priorities[Math.floor(Math.random() * priorities.length)],
        status: status,
        qaAssignedId: qaAssigned.id,
        qaAssigned: qaAssigned,
        devAssignedId: devAssigned.id,
        devAssigned: devAssigned,
        reportedById: reportedBy.id,
        reportedBy: reportedBy,
        issueDate: issueDate,
        expectedFixDate: expectedFixDate,
        actualFixDate: actualFixDate,
        closedDate: closedDate,
        attachments: [],
        comments: [],
        auditLogs: [{
          id: generateId(),
          bugId: '',
          action: BugAuditAction.CREATED,
          performedById: reportedBy.id,
          performedBy: reportedBy,
          performedAt: issueDate,
          description: `Bug created by ${reportedBy.fullName}`
        }],
        watchers: [],
        tags: ['bug', 'frontend'],
        estimatedHours: Math.floor(Math.random() * 16) + 1,
        reopenCount: 0,
        createdAt: issueDate,
        updatedAt: new Date()
      };

      bug.auditLogs[0].bugId = bug.id;

      // Add some random watchers (including reporter and assignees)
      const potentialWatchers = [reportedBy, qaAssigned, devAssigned];
      const additionalWatchers = users.filter(u => !potentialWatchers.some(pw => pw.id === u.id))
        .slice(0, Math.floor(Math.random() * 3)); // 0-2 additional watchers

      const allWatchers = [...potentialWatchers, ...additionalWatchers];

      bug.watchers = allWatchers.map(watcher => ({
        id: generateId(),
        bugId: bug.id,
        userId: watcher.id,
        user: watcher,
        watchedAt: new Date(issueDate.getTime() + Math.random() * (Date.now() - issueDate.getTime())),
        notificationPreferences: {
          ...DEFAULT_WATCHER_PREFERENCES,
          emailNotifications: Math.random() > 0.3, // 70% chance of email notifications
          onComment: Math.random() > 0.2 // 80% chance of comment notifications
        },
        addedById: reportedBy.id,
        addedBy: reportedBy
      }));

      mockBugs.push(bug);
    }

    return mockBugs;
  }
}
