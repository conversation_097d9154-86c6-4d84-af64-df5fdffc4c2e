import { Injectable, inject } from '@angular/core';
import { Observable, BehaviorSubject, of, throwError } from 'rxjs';
import { delay, map, tap } from 'rxjs/operators';
import { Router } from '@angular/router';
import { 
  AuthUser, 
  LoginRequest, 
  LoginResponse, 
  UserRole,
  ROLE_PERMISSIONS,
  hasPermission
} from '../models/user.model';
import { UserService } from './user.service';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private router = inject(Router);
  private userService = inject(UserService);
  
  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);
  private currentUserSubject = new BehaviorSubject<AuthUser | null>(null);

  isAuthenticated$ = this.isAuthenticatedSubject.asObservable();
  currentUser$ = this.currentUserSubject.asObservable();

  constructor() {
    this.initializeAuth();
  }

  login(request: LoginRequest): Observable<LoginResponse> {
    // Mock authentication - in real app, this would call backend API
    return of(null).pipe(
      delay(1000), // Simulate API call
      map(() => {
        // Mock user lookup by email
        const mockUsers = this.getMockAuthUsers();
        const user = mockUsers.find(u => u.email === request.email);
        
        if (!user) {
          throw new Error('Invalid email or password');
        }

        // Mock password validation (in real app, backend would handle this)
        if (request.password !== 'password123') {
          throw new Error('Invalid email or password');
        }

        const authUser: AuthUser = {
          ...user,
          accessToken: this.generateMockToken(),
          refreshToken: this.generateMockToken()
        };

        // Update authentication state
        this.setAuthenticatedUser(authUser);

        return {
          user: authUser,
          accessToken: authUser.accessToken,
          refreshToken: authUser.refreshToken,
          expiresIn: 3600 // 1 hour
        };
      })
    );
  }

  logout(): Observable<void> {
    return of(undefined).pipe(
      delay(200),
      tap(() => {
        this.clearAuthenticatedUser();
        this.router.navigate(['/auth/login']);
      })
    );
  }

  refreshToken(): Observable<string> {
    // Mock token refresh
    return of(this.generateMockToken()).pipe(
      delay(300),
      tap(newToken => {
        const currentUser = this.currentUserSubject.value;
        if (currentUser) {
          const updatedUser = { ...currentUser, accessToken: newToken };
          this.setAuthenticatedUser(updatedUser);
        }
      })
    );
  }

  getCurrentUser(): AuthUser | null {
    return this.currentUserSubject.value;
  }

  isAuthenticated(): boolean {
    return this.isAuthenticatedSubject.value;
  }

  hasRole(role: UserRole): boolean {
    const currentUser = this.getCurrentUser();
    return currentUser?.role === role;
  }

  hasAnyRole(roles: UserRole[]): boolean {
    const currentUser = this.getCurrentUser();
    return currentUser ? roles.includes(currentUser.role) : false;
  }

  hasPermission(permission: string): boolean {
    const currentUser = this.getCurrentUser();
    if (!currentUser) return false;
    
    // Admin has all permissions
    if (currentUser.role === UserRole.ADMIN) return true;
    
    return hasPermission(currentUser.role, permission);
  }

  hasAnyPermission(permissions: string[]): boolean {
    return permissions.some(permission => this.hasPermission(permission));
  }

  canAccessRoute(requiredRoles?: UserRole[], requiredPermissions?: string[]): boolean {
    if (!this.isAuthenticated()) return false;

    const currentUser = this.getCurrentUser();
    if (!currentUser) return false;

    // Check role requirements
    if (requiredRoles && requiredRoles.length > 0) {
      if (!this.hasAnyRole(requiredRoles)) return false;
    }

    // Check permission requirements
    if (requiredPermissions && requiredPermissions.length > 0) {
      if (!this.hasAnyPermission(requiredPermissions)) return false;
    }

    return true;
  }

  // Mock user switching for development/testing
  switchUser(role: UserRole): void {
    const mockUsers = this.getMockAuthUsers();
    const user = mockUsers.find(u => u.role === role);
    
    if (user) {
      const authUser: AuthUser = {
        ...user,
        accessToken: this.generateMockToken(),
        refreshToken: this.generateMockToken()
      };
      this.setAuthenticatedUser(authUser);
    }
  }

  private initializeAuth(): void {
    // Check if user is already authenticated (from localStorage)
    const currentUser = this.userService.getCurrentUser();
    if (currentUser) {
      this.setAuthenticatedUser(currentUser);
    }
  }

  private setAuthenticatedUser(user: AuthUser): void {
    this.currentUserSubject.next(user);
    this.isAuthenticatedSubject.next(true);
    this.userService.setCurrentUser(user);
  }

  private clearAuthenticatedUser(): void {
    this.currentUserSubject.next(null);
    this.isAuthenticatedSubject.next(false);
    this.userService.logout();
  }

  private generateMockToken(): string {
    return 'mock-token-' + Math.random().toString(36).substr(2, 9);
  }

  private getMockAuthUsers(): AuthUser[] {
    return [
      {
        id: 'user-1',
        email: '<EMAIL>',
        fullName: 'Admin User',
        role: UserRole.ADMIN,
        permissions: ROLE_PERMISSIONS[UserRole.ADMIN],
        accessToken: '',
        refreshToken: ''
      },
      {
        id: 'user-2',
        email: '<EMAIL>',
        fullName: 'John Doe',
        role: UserRole.DEVELOPER,
        permissions: ROLE_PERMISSIONS[UserRole.DEVELOPER],
        accessToken: '',
        refreshToken: ''
      },
      {
        id: 'user-3',
        email: '<EMAIL>',
        fullName: 'Jane Smith',
        role: UserRole.QA_ENGINEER,
        permissions: ROLE_PERMISSIONS[UserRole.QA_ENGINEER],
        accessToken: '',
        refreshToken: ''
      },
      {
        id: 'user-4',
        email: '<EMAIL>',
        fullName: 'Mike Wilson',
        role: UserRole.PROJECT_MANAGER,
        permissions: ROLE_PERMISSIONS[UserRole.PROJECT_MANAGER],
        accessToken: '',
        refreshToken: ''
      },
      {
        id: 'user-5',
        email: '<EMAIL>',
        fullName: 'Sarah Johnson',
        role: UserRole.BUSINESS_ANALYST,
        permissions: ROLE_PERMISSIONS[UserRole.BUSINESS_ANALYST],
        accessToken: '',
        refreshToken: ''
      }
    ];
  }

  // Development helper methods
  getAvailableTestUsers(): { email: string; role: UserRole; password: string }[] {
    return [
      { email: '<EMAIL>', role: UserRole.ADMIN, password: 'password123' },
      { email: '<EMAIL>', role: UserRole.DEVELOPER, password: 'password123' },
      { email: '<EMAIL>', role: UserRole.QA_ENGINEER, password: 'password123' },
      { email: '<EMAIL>', role: UserRole.PROJECT_MANAGER, password: 'password123' },
      { email: '<EMAIL>', role: UserRole.BUSINESS_ANALYST, password: 'password123' }
    ];
  }
}
