import { Component, EventEmitter, Output, inject, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { AuthService } from '../../../core/services/auth.service';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './header.component.html',
  styleUrl: './header.component.scss'
})
export class HeaderComponent {
  @Output() sidebarToggle = new EventEmitter<boolean>();

  private router = inject(Router);
  private authService = inject(AuthService);
  
  isSidebarCollapsed = false;
  isUserMenuOpen = false;

  // Mock user data - will be replaced with real auth service
  currentUser = {
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Developer',
    avatar: null
  };

  toggleSidebar() {
    this.isSidebarCollapsed = !this.isSidebarCollapsed;
    this.sidebarToggle.emit(this.isSidebarCollapsed);
  }

  toggleUserMenu() {
    this.isUserMenuOpen = !this.isUserMenuOpen;
  }

  closeUserMenu() {
    this.isUserMenuOpen = false;
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event) {
    const target = event.target as HTMLElement;
    const userMenu = target.closest('.header__user-menu');
    if (!userMenu && this.isUserMenuOpen) {
      this.closeUserMenu();
    }
  }

  navigateToProfile() {
    this.router.navigate(['/profile']);
    this.closeUserMenu();
  }

  navigateToSettings() {
    this.router.navigate(['/settings']);
    this.closeUserMenu();
  }

  logout() {
    this.authService.logout().subscribe({
      next: () => {
        this.closeUserMenu();
        // Navigation to login is handled by the auth service
      },
      error: (error) => {
        console.error('Logout error:', error);
        this.closeUserMenu();
      }
    });
  }

  getInitials(name: string): string {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  }
}
