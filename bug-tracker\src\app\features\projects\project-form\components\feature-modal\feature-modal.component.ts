import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';
import { ModalComponent } from '../../../../../shared/components/modal/modal.component';

export interface FeatureFormData {
  name: string;
  description: string;
}

@Component({
  selector: 'app-feature-modal',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, ModalComponent],
  template: `
    <app-modal 
      [isVisible]="isVisible"
      [title]="'Add Features to ' + moduleName"
      [showFooter]="true"
      [maxWidth]="'700px'"
      (modalClose)="onCancel()">
      
      <form [formGroup]="featuresForm" (ngSubmit)="onSubmit()">
        <div class="form-content">
          <div class="features-header">
            <p class="features-description">Add multiple features to this module. You can add as many as needed.</p>
            <button type="button" class="btn btn-outline btn-sm" (click)="addFeature()">
              <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                <rect x="11" y="4" width="2" height="16" rx="1"></rect>
                <rect x="4" y="11" width="16" height="2" rx="1"></rect>
              </svg>
              Add Another Feature
            </button>
          </div>

          <div formArrayName="features" class="features-list">
            <div *ngFor="let feature of features.controls; let i = index" 
                 [formGroupName]="i" 
                 class="feature-item">
              <div class="feature-header">
                <h4>Feature {{ i + 1 }}</h4>
                <button 
                  type="button" 
                  class="btn-icon btn-icon--danger" 
                  (click)="removeFeature(i)"
                  [disabled]="features.length === 1"
                  title="Remove feature">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                  </svg>
                </button>
              </div>

              <div class="feature-form">
                <div class="form-group">
                  <label class="form-label required">Feature Name</label>
                  <input
                    type="text"
                    class="form-control"
                    formControlName="name"
                    placeholder="Enter feature name"
                    [class.error]="feature.get('name')?.invalid && feature.get('name')?.touched">
                  <div class="form-error" *ngIf="feature.get('name')?.invalid && feature.get('name')?.touched">
                    <span *ngIf="feature.get('name')?.errors?.['required']">Feature name is required</span>
                    <span *ngIf="feature.get('name')?.errors?.['minlength']">Feature name must be at least 2 characters</span>
                    <span *ngIf="feature.get('name')?.errors?.['maxlength']">Feature name cannot exceed 100 characters</span>
                  </div>
                </div>

                <div class="form-group">
                  <label class="form-label">Description</label>
                  <textarea
                    class="form-control"
                    formControlName="description"
                    rows="3"
                    placeholder="Describe this feature (optional)"
                    [class.error]="feature.get('description')?.invalid && feature.get('description')?.touched"></textarea>
                  <div class="form-error" *ngIf="feature.get('description')?.invalid && feature.get('description')?.touched">
                    <span *ngIf="feature.get('description')?.errors?.['maxlength']">Description cannot exceed 500 characters</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>

      <div slot="footer" class="modal-footer-actions">
        <button type="button" class="btn btn-outline" (click)="onCancel()">Cancel</button>
        <button 
          type="button" 
          class="btn btn-primary" 
          [disabled]="featuresForm.invalid || saving"
          (click)="onSubmit()">
          {{ saving ? 'Adding...' : 'Add Features (' + features.length + ')' }}
        </button>
      </div>
    </app-modal>
  `,
  styleUrl: './feature-modal.component.scss'
})
export class FeatureModalComponent implements OnInit, OnChanges {
  @Input() isVisible = false;
  @Input() moduleName = '';
  @Input() saving = false;
  
  @Output() featuresSubmit = new EventEmitter<FeatureFormData[]>();
  @Output() modalCancel = new EventEmitter<void>();

  featuresForm!: FormGroup;

  constructor(private fb: FormBuilder) {
    this.initializeForm();
  }

  ngOnInit() {
    this.initializeForm();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['isVisible'] && this.isVisible && this.featuresForm) {
      this.resetForm();
    }
  }

  get features(): FormArray {
    return this.featuresForm.get('features') as FormArray;
  }

  get feature() {
    return (index: number) => this.features.at(index);
  }

  private initializeForm() {
    this.featuresForm = this.fb.group({
      features: this.fb.array([this.createFeatureFormGroup()])
    });
  }

  private createFeatureFormGroup(): FormGroup {
    return this.fb.group({
      name: ['', [
        Validators.required,
        Validators.minLength(2),
        Validators.maxLength(100)
      ]],
      description: ['', [
        Validators.maxLength(500)
      ]]
    });
  }

  addFeature() {
    this.features.push(this.createFeatureFormGroup());
  }

  removeFeature(index: number) {
    if (this.features.length > 1) {
      this.features.removeAt(index);
    }
  }

  private resetForm() {
    this.featuresForm = this.fb.group({
      features: this.fb.array([this.createFeatureFormGroup()])
    });
  }

  onSubmit() {
    if (this.featuresForm.valid) {
      const formValue = this.featuresForm.value;
      const features: FeatureFormData[] = formValue.features.map((feature: any) => ({
        name: feature.name.trim(),
        description: feature.description?.trim() || ''
      }));
      this.featuresSubmit.emit(features);
    } else {
      this.markFormGroupTouched();
    }
  }

  onCancel() {
    this.resetForm();
    this.modalCancel.emit();
  }

  private markFormGroupTouched() {
    this.features.controls.forEach(control => {
      Object.keys(control.value).forEach(key => {
        control.get(key)?.markAsTouched();
      });
    });
  }
}
