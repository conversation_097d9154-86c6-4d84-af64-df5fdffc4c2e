import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router, ActivatedRoute } from '@angular/router';
import { FormBuilder, FormGroup, FormArray, Validators, ReactiveFormsModule } from '@angular/forms';
import { ProjectService } from '../../../core/services/project.service';
import { UserService } from '../../../core/services/user.service';
import { Project, CreateProjectRequest, UpdateProjectRequest, ProjectStatus } from '../../../core/models/project.model';
import { User, UserRole } from '../../../core/models/user.model';
import { ModuleModalComponent, ModuleFormData } from './components/module-modal/module-modal.component';
import { FeatureModalComponent, FeatureFormData } from './components/feature-modal/feature-modal.component';
import { TeamSelectorComponent, TeamMemberSelection } from './components/team-selector/team-selector.component';

@Component({
  selector: 'app-project-form',
  standalone: true,
  imports: [CommonModule, RouterModule, ReactiveFormsModule, ModuleModalComponent, FeatureModalComponent, TeamSelectorComponent],
  template: `
    <div class="page-container">
      <div class="page-header">
        <div class="project-form__header">
          <h1>{{ isEditMode ? 'Edit Project' : 'Create Project' }}</h1>
          <div class="header-actions">
            <button type="button" class="btn btn-outline" (click)="cancel()">Cancel</button>
            <button
              type="submit"
              class="btn btn-primary"
              [disabled]="projectForm.invalid || saving"
              (click)="onSubmit()">
              {{ saving ? 'Saving...' : (isEditMode ? 'Update Project' : 'Create Project') }}
            </button>
          </div>
        </div>
      </div>

      <form [formGroup]="projectForm" (ngSubmit)="onSubmit()" class="project-form">
        <!-- Basic Information -->
        <div class="form-section">
          <div class="section-header">
            <h2>Basic Information</h2>
            <p>Provide the basic details about your project</p>
          </div>

          <div class="form-grid">
            <div class="form-group">
              <label for="name" class="form-label required">Project Name</label>
              <input
                type="text"
                id="name"
                class="form-control"
                formControlName="name"
                placeholder="Enter project name"
                [class.error]="projectForm.get('name')?.invalid && projectForm.get('name')?.touched">
              <div class="form-error" *ngIf="projectForm.get('name')?.invalid && projectForm.get('name')?.touched">
                <span *ngIf="projectForm.get('name')?.errors?.['required']">Project name is required</span>
                <span *ngIf="projectForm.get('name')?.errors?.['minlength']">Project name must be at least 3 characters</span>
              </div>
            </div>

            <div class="form-group">
              <label for="projectUrl" class="form-label required">Project URL</label>
              <input
                type="url"
                id="projectUrl"
                class="form-control"
                formControlName="projectUrl"
                placeholder="https://example.com"
                [class.error]="projectForm.get('projectUrl')?.invalid && projectForm.get('projectUrl')?.touched">
              <div class="form-error" *ngIf="projectForm.get('projectUrl')?.invalid && projectForm.get('projectUrl')?.touched">
                <span *ngIf="projectForm.get('projectUrl')?.errors?.['required']">Project URL is required</span>
                <span *ngIf="projectForm.get('projectUrl')?.errors?.['pattern']">Please enter a valid URL</span>
              </div>
            </div>

            <div class="form-group form-group--full">
              <label for="description" class="form-label required">Description</label>
              <textarea
                id="description"
                class="form-control"
                formControlName="description"
                rows="4"
                placeholder="Describe your project..."
                [class.error]="projectForm.get('description')?.invalid && projectForm.get('description')?.touched"></textarea>
              <div class="form-error" *ngIf="projectForm.get('description')?.invalid && projectForm.get('description')?.touched">
                <span *ngIf="projectForm.get('description')?.errors?.['required']">Description is required</span>
                <span *ngIf="projectForm.get('description')?.errors?.['minlength']">Description must be at least 10 characters</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Modules and Features -->
        <div class="form-section">
          <div class="section-header">
            <h2>Modules & Features</h2>
            <p>Define the modules and features for your project</p>
            <button type="button" class="btn btn-secondary" (click)="openModuleModal()">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <rect x="11" y="4" width="2" height="16" rx="1"></rect>
                <rect x="4" y="11" width="16" height="2" rx="1"></rect>
              </svg>
              Add Module
            </button>
          </div>

          <!-- Modules Grid -->
          <div class="modules-grid" *ngIf="projectModules.length > 0; else noModules">
            <div class="module-card" *ngFor="let module of projectModules; let i = index">
              <div class="module-header">
                <div class="module-title">
                  <h3>{{ module.name }}</h3>
                  <span class="feature-count">{{ module.features.length }} features</span>
                </div>
                <div class="module-actions">
                  <button
                    type="button"
                    class="btn-icon"
                    (click)="openFeatureModal(i)"
                    title="Add features">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                      <rect x="11" y="4" width="2" height="16" rx="1"></rect>
                      <rect x="4" y="11" width="16" height="2" rx="1"></rect>
                    </svg>
                  </button>
                  <button
                    type="button"
                    class="btn-icon"
                    (click)="editModule(i)"
                    title="Edit module">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                    </svg>
                  </button>
                  <button
                    type="button"
                    class="btn-icon btn-icon--danger"
                    (click)="removeModule(i)"
                    title="Remove module">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                    </svg>
                  </button>
                </div>
              </div>

              <div class="module-content">
                <p class="module-description" *ngIf="module.description">{{ module.description }}</p>

                <!-- Features Accordion -->
                <div class="features-accordion">
                  <button
                    type="button"
                    class="accordion-toggle"
                    [class.expanded]="module.featuresExpanded"
                    (click)="toggleFeatures(i)">
                    <span>Features ({{ module.features.length }})</span>
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"
                         [class.rotated]="module.featuresExpanded">
                      <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
                    </svg>
                  </button>

                  <div class="accordion-content" [class.expanded]="module.featuresExpanded">
                    <div class="features-list" *ngIf="module.features.length > 0; else noFeatures">
                      <div class="feature-item" *ngFor="let feature of module.features">
                        <div class="feature-info">
                          <h5>{{ feature.name }}</h5>
                          <p *ngIf="feature.description">{{ feature.description }}</p>
                        </div>
                      </div>
                    </div>
                    <ng-template #noFeatures>
                      <div class="no-features">
                        <p>No features added yet.</p>
                        <button type="button" class="btn btn-sm btn-outline" (click)="openFeatureModal(i)">
                          Add Features
                        </button>
                      </div>
                    </ng-template>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <ng-template #noModules>
            <div class="empty-modules-state">
              <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="9" y1="9" x2="15" y2="15"></line>
                <line x1="15" y1="9" x2="9" y2="15"></line>
              </svg>
              <h3>No Modules Added</h3>
              <p>Start by adding your first module to organize your project features.</p>
              <button type="button" class="btn btn-primary" (click)="openModuleModal()">
                Add First Module
              </button>
            </div>
          </ng-template>
        </div>

        <!-- Team Members -->
        <div class="form-section">
          <app-team-selector
            [selectedTeamMembers]="selectedTeamMembers"
            (teamMembersChange)="onTeamMembersChange($event)">
          </app-team-selector>
        </div>
      </form>

      <!-- Module Modal -->
      <app-module-modal
        [isVisible]="showModuleModal"
        [moduleData]="editingModuleData"
        [saving]="saving"
        (moduleSubmit)="onModuleSubmit($event)"
        (modalCancel)="onModuleModalCancel()">
      </app-module-modal>

      <!-- Feature Modal -->
      <app-feature-modal
        [isVisible]="showFeatureModal"
        [moduleName]="selectedModuleName"
        [saving]="saving"
        (featuresSubmit)="onFeaturesSubmit($event)"
        (modalCancel)="onFeatureModalCancel()">
      </app-feature-modal>
    </div>
  `,
  styles: [`
    .project-form__header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .project-form__header h1 {
      font-size: var(--font-size-3xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-gray-900);
      margin: 0;
    }

    .header-actions {
      display: flex;
      gap: var(--spacing-3);
    }

    /* Form Sections */
    .form-section {
      background: var(--color-white);
      border: 1px solid var(--color-gray-200);
      border-radius: var(--border-radius-lg);
      padding: var(--spacing-6);
      margin-bottom: var(--spacing-6);
    }

    .section-header {
      margin-bottom: var(--spacing-6);
      padding-bottom: var(--spacing-4);
      border-bottom: 1px solid var(--color-gray-100);
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      flex-wrap: wrap;
      gap: var(--spacing-4);
    }

    .section-header h2 {
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-900);
      margin: 0;
    }

    .section-header p {
      color: var(--color-gray-600);
      margin: var(--spacing-1) 0 0 0;
      font-size: var(--font-size-sm);
    }

    /* Form Grid */
    .form-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-4);
    }

    .form-group--full {
      grid-column: 1 / -1;
    }

    .form-group {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-2);
    }

    .form-label {
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-700);
    }

    .form-label.required::after {
      content: ' *';
      color: var(--color-error-500);
    }

    .form-control {
      padding: var(--spacing-3);
      border: 1px solid var(--color-gray-300);
      border-radius: var(--border-radius-md);
      font-size: var(--font-size-sm);
      transition: all var(--transition-fast);
    }

    .form-control:focus {
      outline: none;
      border-color: var(--color-primary-500);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .form-control.error {
      border-color: var(--color-error-500);
    }

    .form-control.error:focus {
      box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
    }

    .form-error {
      font-size: var(--font-size-xs);
      color: var(--color-error-600);
      margin-top: var(--spacing-1);
    }

    /* Modules Grid */
    .modules-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
      gap: var(--spacing-6);
    }

    .module-card {
      background: var(--color-white);
      border: 1px solid var(--color-gray-200);
      border-radius: var(--border-radius-lg);
      padding: var(--spacing-6);
      transition: all var(--transition-fast);

      &:hover {
        border-color: var(--color-gray-300);
        box-shadow: var(--shadow-md);
      }
    }

    .module-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: var(--spacing-4);
    }

    .module-title h3 {
      margin: 0 0 var(--spacing-1) 0;
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-900);
    }

    .feature-count {
      font-size: var(--font-size-sm);
      color: var(--color-gray-600);
      font-weight: var(--font-weight-medium);
    }

    .module-actions {
      display: flex;
      gap: var(--spacing-2);
    }

    .module-content {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-4);
    }

    .module-description {
      margin: 0;
      color: var(--color-gray-600);
      font-size: var(--font-size-sm);
      line-height: 1.5;
    }

    .btn-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border: 1px solid var(--color-gray-300);
      border-radius: var(--border-radius-md);
      background: var(--color-white);
      color: var(--color-gray-600);
      cursor: pointer;
      transition: all var(--transition-fast);
    }

    .btn-icon:hover:not(:disabled) {
      border-color: var(--color-primary-300);
      color: var(--color-primary-600);
      background: var(--color-primary-50);
    }

    .btn-icon--danger:hover:not(:disabled) {
      border-color: var(--color-error-300);
      color: var(--color-error-600);
      background: var(--color-error-50);
    }

    .btn-icon:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    /* Features Accordion */
    .features-accordion {
      border: 1px solid var(--color-gray-200);
      border-radius: var(--border-radius-md);
      overflow: hidden;
    }

    .accordion-toggle {
      width: 100%;
      background: var(--color-gray-50);
      border: none;
      padding: var(--spacing-3) var(--spacing-4);
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-700);
      transition: all var(--transition-fast);

      &:hover {
        background: var(--color-gray-100);
      }

      &.expanded {
        background: var(--color-primary-50);
        color: var(--color-primary-700);
      }

      svg {
        transition: transform var(--transition-fast);

        &.rotated {
          transform: rotate(180deg);
        }
      }
    }

    .accordion-content {
      max-height: 0;
      overflow: hidden;
      transition: max-height var(--transition-normal);

      &.expanded {
        max-height: 500px;
      }
    }

    .features-list {
      padding: var(--spacing-4);
      display: flex;
      flex-direction: column;
      gap: var(--spacing-3);
    }

    .feature-item {
      background: var(--color-white);
      border: 1px solid var(--color-gray-200);
      border-radius: var(--border-radius-md);
      padding: var(--spacing-3);
    }

    .feature-info h5 {
      margin: 0 0 var(--spacing-1) 0;
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-900);
    }

    .feature-info p {
      margin: 0;
      font-size: var(--font-size-xs);
      color: var(--color-gray-600);
      line-height: 1.4;
    }

    .no-features {
      padding: var(--spacing-4);
      text-align: center;
      color: var(--color-gray-500);

      p {
        margin: 0 0 var(--spacing-3) 0;
        font-size: var(--font-size-sm);
      }
    }

    /* Empty States */
    .empty-modules-state {
      text-align: center;
      padding: var(--spacing-12) var(--spacing-6);
      color: var(--color-gray-500);

      svg {
        margin-bottom: var(--spacing-4);
        opacity: 0.6;
      }

      h3 {
        margin: 0 0 var(--spacing-2) 0;
        font-size: var(--font-size-lg);
        color: var(--color-gray-700);
      }

      p {
        margin: 0 0 var(--spacing-4) 0;
        font-size: var(--font-size-sm);
      }
    }

    /* Team Selection - Now handled by TeamSelectorComponent */

    /* Responsive */
    @media (max-width: 768px) {
      .form-grid {
        grid-template-columns: 1fr;
      }

      .modules-grid {
        grid-template-columns: 1fr;
      }

      .module-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-3);
      }

      .module-actions {
        align-self: flex-end;
      }

      .section-header {
        flex-direction: column;
        align-items: flex-start;
      }

      .accordion-content.expanded {
        max-height: 300px;
      }
    }
  `]
})
export class ProjectFormComponent implements OnInit {
  private fb = inject(FormBuilder);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private projectService = inject(ProjectService);
  private userService = inject(UserService);

  projectForm!: FormGroup;
  isEditMode = false;
  projectId: string | null = null;
  saving = false;
  users: User[] = [];
  selectedTeamMembers: TeamMemberSelection[] = [];

  // Modal states
  showModuleModal = false;
  showFeatureModal = false;
  editingModuleData: ModuleFormData | null = null;
  selectedModuleName = '';
  selectedModuleIndex = -1;

  // Project modules (separate from form array for easier management)
  projectModules: Array<{
    name: string;
    description: string;
    features: Array<{ name: string; description: string }>;
    featuresExpanded: boolean;
  }> = [];

  ngOnInit() {
    this.initializeForm();
    this.loadUsers();
    this.checkEditMode();
  }

  private initializeForm() {
    this.projectForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(3)]],
      description: ['', [Validators.required, Validators.minLength(10)]],
      projectUrl: ['', [Validators.required, Validators.pattern(/^https?:\/\/.+/)]]
    });
  }

  private loadUsers() {
    this.users = this.userService.getAllUsers();
  }

  private checkEditMode() {
    this.projectId = this.route.snapshot.paramMap.get('id');
    if (this.projectId) {
      this.isEditMode = true;
      this.loadProject();
    }
  }

  private loadProject() {
    if (!this.projectId) return;

    this.projectService.getProjectById(this.projectId).subscribe({
      next: (project) => {
        if (project) {
          this.populateForm(project);
        }
      },
      error: (error) => {
        console.error('Error loading project:', error);
        this.router.navigate(['/projects']);
      }
    });
  }

  private populateForm(project: Project) {
    this.projectForm.patchValue({
      name: project.name,
      description: project.description,
      projectUrl: project.projectUrl
    });

    // Populate project modules
    this.projectModules = project.modules.map(module => ({
      name: module.name,
      description: module.description || '',
      features: module.features.map(feature => ({
        name: feature.name,
        description: feature.description || ''
      })),
      featuresExpanded: false
    }));

    // Set selected team members
    this.selectedTeamMembers = project.teamMembers.map(member => ({
      userId: member.userId,
      user: member.user,
      role: member.role
    }));
  }

  // Old form array methods - no longer used with new grid layout

  onSubmit() {
    if (this.projectForm.invalid) {
      this.markFormGroupTouched(this.projectForm);
      return;
    }

    this.saving = true;
    const formValue = this.projectForm.value;

    const projectData = {
      name: formValue.name,
      description: formValue.description,
      projectUrl: formValue.projectUrl,
      modules: this.projectModules.map(module => ({
        name: module.name,
        description: module.description,
        features: module.features.map(feature => ({
          name: feature.name,
          description: feature.description
        }))
      })),
      teamMembers: this.selectedTeamMembers.map(member => ({
        userId: member.userId,
        role: member.role
      }))
    };

    if (this.isEditMode && this.projectId) {
      this.updateProject(projectData);
    } else {
      this.createProject(projectData);
    }
  }

  private createProject(projectData: CreateProjectRequest) {
    this.projectService.createProject(projectData).subscribe({
      next: (response) => {
        this.saving = false;
        if (response.data) {
          this.router.navigate(['/projects', response.data.id]);
        }
      },
      error: (error) => {
        console.error('Error creating project:', error);
        this.saving = false;
      }
    });
  }

  private updateProject(projectData: UpdateProjectRequest) {
    if (!this.projectId) return;

    this.projectService.updateProject(this.projectId, projectData).subscribe({
      next: (response) => {
        this.saving = false;
        this.router.navigate(['/projects', this.projectId]);
      },
      error: (error) => {
        console.error('Error updating project:', error);
        this.saving = false;
      }
    });
  }

  cancel() {
    this.router.navigate(['/projects']);
  }

  // Modal methods
  openModuleModal() {
    this.editingModuleData = null;
    this.showModuleModal = true;
  }

  editModule(index: number) {
    const module = this.projectModules[index];
    this.editingModuleData = {
      name: module.name,
      description: module.description
    };
    this.selectedModuleIndex = index;
    this.showModuleModal = true;
  }

  onModuleSubmit(moduleData: ModuleFormData) {
    if (this.selectedModuleIndex >= 0) {
      // Edit existing module
      this.projectModules[this.selectedModuleIndex] = {
        ...this.projectModules[this.selectedModuleIndex],
        name: moduleData.name,
        description: moduleData.description
      };
    } else {
      // Add new module
      this.projectModules.push({
        name: moduleData.name,
        description: moduleData.description,
        features: [],
        featuresExpanded: false
      });
    }
    this.onModuleModalCancel();
  }

  onModuleModalCancel() {
    this.showModuleModal = false;
    this.editingModuleData = null;
    this.selectedModuleIndex = -1;
  }

  openFeatureModal(moduleIndex: number) {
    this.selectedModuleIndex = moduleIndex;
    this.selectedModuleName = this.projectModules[moduleIndex].name;
    this.showFeatureModal = true;
  }

  onFeaturesSubmit(features: FeatureFormData[]) {
    if (this.selectedModuleIndex >= 0) {
      this.projectModules[this.selectedModuleIndex].features.push(...features);
    }
    this.onFeatureModalCancel();
  }

  onFeatureModalCancel() {
    this.showFeatureModal = false;
    this.selectedModuleName = '';
    this.selectedModuleIndex = -1;
  }

  removeModule(index: number) {
    this.projectModules.splice(index, 1);
  }

  toggleFeatures(index: number) {
    this.projectModules[index].featuresExpanded = !this.projectModules[index].featuresExpanded;
  }

  onTeamMembersChange(teamMembers: TeamMemberSelection[]) {
    this.selectedTeamMembers = teamMembers;
  }

  private markFormGroupTouched(formGroup: FormGroup | FormArray) {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      if (control instanceof FormGroup || control instanceof FormArray) {
        this.markFormGroupTouched(control);
      } else {
        control?.markAsTouched();
      }
    });
  }
}
