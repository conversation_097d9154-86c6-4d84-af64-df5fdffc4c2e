.main-layout {
  display: flex;
  min-height: 100vh;
  background: #f8fafc;

  &__header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: var(--z-index-fixed);
    background-color: var(--color-white);
    border-bottom: 1px solid var(--color-gray-200);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    height: 72px;
  }

  &__sidebar {
    position: fixed;
    top: 72px;
    left: 0;
    bottom: 0;
    width: 280px;
    z-index: var(--z-index-sticky);
    background-color: var(--color-white);
    border-right: 1px solid var(--color-gray-200);
    transition: width var(--transition-base);
    overflow: hidden;

    &.collapsed {
      width: 64px;
    }
  }

  &__content {
    flex: 1;
    margin-top: 72px;
    margin-left: 280px;
    min-height: calc(100vh - 72px);
    transition: margin-left var(--transition-base);
    background: transparent;

    &--expanded {
      margin-left: 64px;
    }

    &-inner {
      padding: var(--spacing-4);
      max-width: none;
      margin: 0;
      background: transparent;
    }
  }

  // Mobile responsive
  @media (max-width: 768px) {
    &__sidebar {
      transform: translateX(-100%);
      width: 100%;
      max-width: 280px;

      &:not(.collapsed) {
        transform: translateX(0);
        width: 280px;
      }
    }

    &__content {
      margin-left: 0;

      &--expanded {
        margin-left: 0;
      }

      &-inner {
        padding: var(--spacing-3);
      }
    }
  }

  // Tablet responsive
  @media (max-width: 1024px) and (min-width: 769px) {
    &__sidebar {
      width: 240px;

      &.collapsed {
        width: 56px;
      }
    }

    &__content {
      margin-left: 240px;

      &--expanded {
        margin-left: 56px;
      }

      &-inner {
        padding: var(--spacing-4);
      }
    }
  }
}
