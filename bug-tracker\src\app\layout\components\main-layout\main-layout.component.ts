import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON>roy, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet } from '@angular/router';
import { HeaderComponent } from '../header/header.component';
import { SidebarComponent } from '../sidebar/sidebar.component';

@Component({
  selector: 'app-main-layout',
  standalone: true,
  imports: [CommonModule, RouterOutlet, HeaderComponent, SidebarComponent],
  templateUrl: './main-layout.component.html',
  styleUrl: './main-layout.component.scss'
})
export class MainLayoutComponent implements OnInit, OnDestroy {
  isSidebarCollapsed = false;
  private isMobile = false;

  ngOnInit() {
    this.checkScreenSize();
  }

  ngOnDestroy() {
    // Cleanup if needed
  }

  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.checkScreenSize();
  }

  private checkScreenSize() {
    // Check if we're in browser environment
    if (typeof window === 'undefined') {
      return;
    }

    const wasMobile = this.isMobile;
    this.isMobile = window.innerWidth <= 768;

    // Auto-collapse on mobile
    if (this.isMobile && !wasMobile) {
      this.isSidebarCollapsed = true;
    }
    // Auto-expand on desktop if it was collapsed due to mobile
    else if (!this.isMobile && wasMobile && this.isSidebarCollapsed) {
      this.isSidebarCollapsed = false;
    }
  }

  onSidebarToggle(collapsed: boolean) {
    this.isSidebarCollapsed = collapsed;
  }
}
