import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ModalComponent } from '../../../../../shared/components/modal/modal.component';

export interface ModuleFormData {
  name: string;
  description: string;
}

@Component({
  selector: 'app-module-modal',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, ModalComponent],
  template: `
    <app-modal 
      [isVisible]="isVisible"
      [title]="isEditMode ? 'Edit Module' : 'Add Module'"
      [showFooter]="true"
      (modalClose)="onCancel()">
      
      <form [formGroup]="moduleForm" (ngSubmit)="onSubmit()">
        <div class="form-content">
          <div class="form-group">
            <label for="moduleName" class="form-label required">Module Name</label>
            <input
              type="text"
              id="moduleName"
              class="form-control"
              formControlName="name"
              placeholder="Enter module name"
              [class.error]="moduleForm.get('name')?.invalid && moduleForm.get('name')?.touched">
            <div class="form-error" *ngIf="moduleForm.get('name')?.invalid && moduleForm.get('name')?.touched">
              <span *ngIf="moduleForm.get('name')?.errors?.['required']">Module name is required</span>
              <span *ngIf="moduleForm.get('name')?.errors?.['minlength']">Module name must be at least 2 characters</span>
              <span *ngIf="moduleForm.get('name')?.errors?.['maxlength']">Module name cannot exceed 100 characters</span>
            </div>
          </div>

          <div class="form-group">
            <label for="moduleDescription" class="form-label">Description</label>
            <textarea
              id="moduleDescription"
              class="form-control"
              formControlName="description"
              rows="4"
              placeholder="Describe this module (optional)"
              [class.error]="moduleForm.get('description')?.invalid && moduleForm.get('description')?.touched"></textarea>
            <div class="form-error" *ngIf="moduleForm.get('description')?.invalid && moduleForm.get('description')?.touched">
              <span *ngIf="moduleForm.get('description')?.errors?.['maxlength']">Description cannot exceed 500 characters</span>
            </div>
          </div>
        </div>
      </form>

      <div slot="footer" class="modal-footer-actions">
        <button type="button" class="btn btn-outline" (click)="onCancel()">Cancel</button>
        <button 
          type="button" 
          class="btn btn-primary" 
          [disabled]="moduleForm.invalid || saving"
          (click)="onSubmit()">
          {{ saving ? 'Saving...' : (isEditMode ? 'Update Module' : 'Add Module') }}
        </button>
      </div>
    </app-modal>
  `,
  styleUrl: './module-modal.component.scss'
})
export class ModuleModalComponent implements OnInit, OnChanges {
  @Input() isVisible = false;
  @Input() moduleData: ModuleFormData | null = null;
  @Input() saving = false;
  
  @Output() moduleSubmit = new EventEmitter<ModuleFormData>();
  @Output() modalCancel = new EventEmitter<void>();

  moduleForm!: FormGroup;
  isEditMode = false;

  constructor(private fb: FormBuilder) {
    this.initializeForm();
  }

  ngOnInit() {
    this.initializeForm();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['moduleData'] && this.moduleForm) {
      this.updateFormWithData();
    }
    if (changes['isVisible'] && this.isVisible && this.moduleForm) {
      this.resetForm();
    }
  }

  private initializeForm() {
    this.moduleForm = this.fb.group({
      name: ['', [
        Validators.required,
        Validators.minLength(2),
        Validators.maxLength(100)
      ]],
      description: ['', [
        Validators.maxLength(500)
      ]]
    });
  }

  private updateFormWithData() {
    if (this.moduleData) {
      this.isEditMode = true;
      this.moduleForm.patchValue({
        name: this.moduleData.name,
        description: this.moduleData.description || ''
      });
    } else {
      this.isEditMode = false;
      this.resetForm();
    }
  }

  private resetForm() {
    this.moduleForm.reset();
    this.moduleForm.patchValue({
      name: '',
      description: ''
    });
    this.isEditMode = false;
  }

  onSubmit() {
    if (this.moduleForm.valid) {
      const formValue = this.moduleForm.value;
      const moduleData: ModuleFormData = {
        name: formValue.name.trim(),
        description: formValue.description?.trim() || ''
      };
      this.moduleSubmit.emit(moduleData);
    } else {
      this.markFormGroupTouched();
    }
  }

  onCancel() {
    this.resetForm();
    this.modalCancel.emit();
  }

  private markFormGroupTouched() {
    Object.keys(this.moduleForm.controls).forEach(key => {
      const control = this.moduleForm.get(key);
      control?.markAsTouched();
    });
  }
}
